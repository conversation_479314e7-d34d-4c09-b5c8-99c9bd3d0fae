// Security Review Modal Handler
// This file manages the security review entry modal

// Button state constants for consistent state management
const BUTTON_STATE = {
  INITIAL: {
    text: "Submit Review",
    disabled: false,
    classes: [],
  },
  LOADING: {
    text: "Submitting...",
    disabled: true,
    classes: ["loading-state"],
  },
};

// Track current button state for debugging and consistency
let currentButtonState = "INITIAL";

// Show security review modal for an endpoint
function showSecurityReviewModal(endpoint) {
  if (!endpoint) {
    console.error("Cannot show security review modal: No endpoint provided");
    if (typeof showError === 'function') {
      showError("Cannot add security review: No endpoint selected");
    }
    return;
  }

  // Get the modal element
  const modal = document.getElementById("securityReviewModal");
  if (!modal) {
    console.error("Security review modal not found in DOM");
    if (typeof showError === 'function') {
      showError("Security review interface not available. Please refresh the page.");
    }
    return;
  }

  // Check if Bootstrap is available
  if (typeof bootstrap === "undefined" || !bootstrap.Modal) {
    console.error("Bootstrap Modal is not available");
    if (typeof showError === 'function') {
      showError("Modal system not available. Please refresh the page.");
    }
    return;
  }

  // Get the Bootstrap modal instance
  const modalInstance = bootstrap.Modal.getOrCreateInstance(modal);

  // Set endpoint information in the modal
  document.getElementById("security-review-endpoint-id").value = endpoint.id;

  // Set HTTP method and route - fix the HTTP method display issue
  const route =
    endpoint.route ||
    endpoint.newValue?.route ||
    endpoint.oldValue?.route ||
    "N/A";

  // Handle HTTP methods properly - check multiple possible sources
  let httpMethod = "N/A";
  if (
    endpoint.httpMethods &&
    Array.isArray(endpoint.httpMethods) &&
    endpoint.httpMethods.length > 0
  ) {
    httpMethod = endpoint.httpMethods.join(", ");
  } else if (endpoint.newValue?.httpMethods && Array.isArray(endpoint.newValue.httpMethods)) {
    httpMethod = endpoint.newValue.httpMethods.join(", ");
  } else if (endpoint.oldValue?.httpMethods && Array.isArray(endpoint.oldValue.httpMethods)) {
    httpMethod = endpoint.oldValue.httpMethods.join(", ");
  } else if (endpoint.httpMethod) {
    httpMethod = endpoint.httpMethod;
  } else if (endpoint.newValue?.httpMethod) {
    httpMethod = endpoint.newValue.httpMethod;
  } else if (endpoint.oldValue?.httpMethod) {
    httpMethod = endpoint.oldValue.httpMethod;
  }

  document.getElementById("security-review-http-method").textContent = httpMethod;
  document.getElementById("security-review-route").textContent = route;

  // Set current date/time and refresh it when the modal opens
  const now = new Date();
  const currentDateTime = formatCurrentDateTime();
  document.getElementById("security-review-datetime-display").textContent = currentDateTime;
  document.getElementById("security-review-datetime").value = now.toISOString();

  // Prefill reviewer username with current logged-in user
  document.getElementById("security-review-username").value = getCurrentUser();

  // Clear other form fields
  document.getElementById("security-review-status").selectedIndex = 0;
  document.getElementById("security-review-notes").value = "";

  // Reset validation state
  resetFormValidation();

  // Reset button state to ensure submit button is in initial state
  resetSubmitButtonState();

  // Show the modal
  modalInstance.show();
}

// Reset form validation state
function resetFormValidation() {
  const form = document.getElementById("securityReviewForm");
  if (!form) return;

  // Remove validation classes
  form.classList.remove("was-validated");

  // Reset all invalid feedback messages
  const invalidFeedbacks = form.querySelectorAll(".invalid-feedback");
  invalidFeedbacks.forEach((feedback) => {
    feedback.style.display = "none";
  });

  // Hide any error alerts
  hideModalError();
}

// Validate the security review form
function validateSecurityReviewForm() {
  const form = document.getElementById("securityReviewForm");
  if (!form) return false;

  // Add validation class to show validation feedback
  form.classList.add("was-validated");

  // Check if form is valid
  const isValid = form.checkValidity();

  // Additional custom validation
  let customValid = true;

  // Validate username pattern
  const usernameInput = document.getElementById("security-review-username");
  const usernamePattern = /^[a-zA-Z0-9._@-]+$/;
  if (usernameInput.value && !usernamePattern.test(usernameInput.value)) {
    showFieldError(usernameInput, "Username contains invalid characters. Only letters, numbers, dots, underscores, @ symbols, and hyphens are allowed.");
    customValid = false;
  } else {
    clearFieldError(usernameInput);
  }

  return isValid && customValid;
}

// Show field-specific error
function showFieldError(input, message) {
  const feedback = input.parentNode.querySelector(".invalid-feedback");
  if (feedback) {
    feedback.textContent = message;
    feedback.style.display = "block";
  }
  input.classList.add("is-invalid");
}

// Clear field-specific error
function clearFieldError(input) {
  const feedback = input.parentNode.querySelector(".invalid-feedback");
  if (feedback) {
    feedback.style.display = "none";
  }
  input.classList.remove("is-invalid");
}

// Handle form submission with enhanced error handling
function handleSecurityReviewSubmit() {
  console.log("🟡 Handling security review form submission");

  try {
    // Validate the form first
    if (!validateSecurityReviewForm()) {
      console.warn("❌ Form validation failed");
      showModalError("Please correct the errors in the form before submitting.");
      return false;
    }
    
    console.log("✅ Form validation passed");

    // Show loading state
    showSubmitButtonLoadingState("Submitting...");

    // Get form data
    const formData = {
      endpointId: document.getElementById("security-review-endpoint-id").value,
      reviewDateTime: document.getElementById("security-review-datetime").value,
      reviewerUsername: document.getElementById("security-review-username").value.trim(),
      securityStatus: document.getElementById("security-review-status").value,
      reviewNotes: document.getElementById("security-review-notes").value.trim(),
    };

    console.log("📋 Form data collected:", formData);
    
    // Check if required functions exist
    console.log("🔍 Function availability check:");
    console.log(`  - createSecurityReview: ${typeof createSecurityReview}`);
    console.log(`  - getSecurityReviewsForEndpoint: ${typeof getSecurityReviewsForEndpoint}`);
    
    // Check review count before creation
    const reviewsBefore = getSecurityReviewsForEndpoint(formData.endpointId);
    console.log(`📊 Reviews before creation: ${reviewsBefore.length}`);

    // Create the security review
    console.log("🚀 Creating security review...");
    const review = createSecurityReview(formData);
    console.log("✅ Security review created:", review);
    
    // Check review count after creation
    const reviewsAfter = getSecurityReviewsForEndpoint(formData.endpointId);
    console.log(`📊 Reviews after creation: ${reviewsAfter.length}`);
    
    if (reviewsAfter.length > reviewsBefore.length) {
      console.log("✅ Review was successfully saved to data store!");
    } else {
      console.log("❌ Review was not saved or was prevented by duplicate detection");
    }
    console.log("Security review created:", review);

    // Automatically trigger save of security reviews to disk after creating a new one
    console.log("🚀 Auto-saving security reviews to disk...");
    
    // Use setTimeout to handle save operation without blocking the UI
    setTimeout(() => {
      try {
        console.log("📄 Triggering auto-save of security reviews");
        
        // Call the save function to trigger download
        if (typeof DataLoader !== 'undefined' && DataLoader.saveSecurityReviewsToFile) {
          DataLoader.saveSecurityReviewsToFile();
          console.log("✅ Security reviews download triggered via DataLoader - please check your downloads folder");
        } else if (typeof saveSecurityReviewsToFile === 'function') {
          saveSecurityReviewsToFile();
          console.log("✅ Security reviews download triggered - please check your downloads folder");
        } else {
          console.warn("❌ saveSecurityReviewsToFile function not available - reviews saved in memory only");
        }
      } catch (saveError) {
        console.error("❌ Error triggering security reviews save:", saveError);
        // Don't block the user if save fails, but show a warning
        if (typeof showWarning === 'function') {
          showWarning("Security review was created but could not be saved automatically. You may need to save manually.", {
            details: saveError.message,
            persistent: false
          });
        }
      }
    }, 100);

    // Close the modal
    const modal = document.getElementById("securityReviewModal");
    const modalInstance = bootstrap.Modal.getInstance(modal);
    if (modalInstance) {
      modalInstance.hide();
    }

    // Show success message
    if (typeof showSuccessMessage === 'function') {
      showSuccessMessage("Security review added successfully! Download should start automatically.");
    } else {
      console.log("Security review added successfully! Download should start automatically.");
    }

    // Refresh the grid to show the new review
    if (typeof refreshGridAfterSecurityReviewChanges === 'function') {
      refreshGridAfterSecurityReviewChanges(formData.endpointId);
    }

    // Reset button state
    resetSubmitButtonState();

    return true;
  } catch (error) {
    console.error("Error submitting security review:", error);

    // Reset button state
    resetSubmitButtonState();

    // Show error message
    if (error instanceof ValidationError) {
      const errorMessage = error.errors && error.errors.length > 0 
        ? error.errors.join("; ") 
        : error.message;
      showModalError(`Validation Error: ${errorMessage}`, {
        allowRetry: true,
        retryCallback: 'retrySecurityReviewSubmit'
      });
    } else {
      showModalError(`Failed to save security review: ${error.message}`, {
        allowRetry: true,
        retryCallback: 'retrySecurityReviewSubmit'
      });
    }

    return false;
  }
}

// Enhanced modal error handling
function showModalError(message, options = {}) {
  const alertElement = document.getElementById("security-review-alert");
  const errorMessageElement = document.getElementById("security-review-error-message");

  if (alertElement && errorMessageElement) {
    // Set the error message
    errorMessageElement.textContent = message;

    // Add retry button if callback is provided
    if (options.allowRetry && options.retryCallback) {
      const retryButton = document.createElement("button");
      retryButton.className = "btn btn-sm btn-outline-danger ms-2";
      retryButton.textContent = "Retry";
      retryButton.onclick = function() {
        if (typeof window[options.retryCallback] === 'function') {
          window[options.retryCallback]();
        }
      };
      
      // Clear any existing retry buttons
      const existingRetryBtn = errorMessageElement.parentNode.querySelector(".btn");
      if (existingRetryBtn) {
        existingRetryBtn.remove();
      }
      
      errorMessageElement.parentNode.appendChild(retryButton);
    }

    // Show the alert
    alertElement.classList.remove("d-none");

    // Auto-hide after delay if not persistent
    if (!options.persistent) {
      setTimeout(() => {
        hideModalError();
      }, options.autoHideDelay || 5000);
    }
  }
}

// Hide error message in the modal
function hideModalError() {
  const alertElement = document.getElementById("security-review-alert");
  if (alertElement) {
    alertElement.classList.add("d-none");
    
    // Remove any retry buttons
    const retryBtn = alertElement.querySelector(".btn");
    if (retryBtn) {
      retryBtn.remove();
    }
  }
}

// Reset submit button to initial state
function resetSubmitButtonState() {
  const submitBtn = document.getElementById("submit-security-review");
  if (submitBtn) {
    const initialState = BUTTON_STATE.INITIAL;
    submitBtn.textContent = initialState.text;
    submitBtn.disabled = initialState.disabled;
    
    // Remove loading classes
    submitBtn.classList.remove(...BUTTON_STATE.LOADING.classes);
    
    currentButtonState = "INITIAL";
  }
}

// Enhanced loading state function that integrates with existing showLoadingState pattern
function showSubmitButtonLoadingState(message = "Submitting...") {
  const submitBtn = document.getElementById("submit-security-review");
  if (submitBtn) {
    const loadingState = BUTTON_STATE.LOADING;
    submitBtn.textContent = message;
    submitBtn.disabled = loadingState.disabled;
    
    // Add loading classes
    submitBtn.classList.add(...loadingState.classes);
    
    currentButtonState = "LOADING";
  }
}

// Get current button state (utility for debugging)
function getCurrentButtonState() {
  return currentButtonState;
}

// Security button click handler function
// NOTE: This handler is no longer used - functionality moved to review-fixes.js
// to avoid conflicts with row click prevention logic
/*
function securityButtonHandler(e) {
  const addReviewBtn = e.target.closest(".add-review-btn");
  if (addReviewBtn) {
    e.preventDefault();
    e.stopPropagation();

    const endpointId = addReviewBtn.getAttribute("data-endpoint-id");
    const httpMethod = addReviewBtn.getAttribute("data-http-method");
    const route = addReviewBtn.getAttribute("data-route");

    console.log(`Add review button clicked for endpoint: ${endpointId}`);
    console.log("Button data:", { endpointId, httpMethod, route });

    // Create endpoint object for the modal
    const endpoint = {
      id: endpointId,
      httpMethods: httpMethod ? httpMethod.split(", ") : ["Unknown"],
      route: route || "Unknown Route"
    };

    if (typeof showSecurityReviewModal === 'function') {
      showSecurityReviewModal(endpoint);
    } else {
      console.error("showSecurityReviewModal function not available");
      alert("Security review modal is not available. Please check the console for errors.");
    }
    return;
  }

  const viewHistoryBtn = e.target.closest(".view-history-btn");
  if (viewHistoryBtn) {
    e.preventDefault();
    e.stopPropagation();

    const endpointId = viewHistoryBtn.getAttribute("data-endpoint-id");
    const httpMethod = viewHistoryBtn.getAttribute("data-http-method");
    const route = viewHistoryBtn.getAttribute("data-route");

    console.log(`View history button clicked for endpoint: ${endpointId}`);
    console.log("Button data:", { endpointId, httpMethod, route });

    // Create endpoint object for the modal
    const endpoint = {
      id: endpointId,
      httpMethods: httpMethod ? httpMethod.split(", ") : ["Unknown"],
      route: route || "Unknown Route"
    };

    if (typeof showSecurityReviewHistoryModal === 'function') {
      showSecurityReviewHistoryModal(endpoint);
    } else {
      console.error("showSecurityReviewHistoryModal function not available");
      alert("Security review history modal is not available. Please check the console for errors.");
    }
    return;
  }
}
*/

// Initialize security action buttons
function initSecurityActionButtons() {
  console.log("Initializing security action buttons");
  
  // Note: The actual click handling is done in review-fixes.js to prevent conflicts
  // with row click handlers. This function is kept for compatibility and may be
  // used for other button initialization tasks.
  
  console.log("Security action button event listeners managed by review-fixes.js");
}

// Real-time form validation
function initRealTimeFormValidation() {
  const form = document.getElementById("securityReviewForm");
  if (!form) return;

  // Add input event listeners for real-time validation
  const inputs = form.querySelectorAll("input, select, textarea");
  inputs.forEach(input => {
    input.addEventListener("input", function() {
      // Clear previous validation state for this field
      clearFieldError(input);
      
      // If form has been validated before, re-validate this field
      if (form.classList.contains("was-validated")) {
        validateSecurityReviewForm();
      }
    });
  });
}

// Initialize modal handlers
function initSecurityReviewModal() {
  console.log("Initializing security review modal handlers");

  // Initialize action buttons
  initSecurityActionButtons();

  // Initialize real-time form validation
  initRealTimeFormValidation();

  // Add submit handler to the form
  const form = document.getElementById("securityReviewForm");
  if (form) {
    form.addEventListener("submit", function(e) {
      e.preventDefault();
      handleSecurityReviewSubmit();
    });
  }

  // Add submit handler to the submit button
  const submitBtn = document.getElementById("submit-security-review");
  if (submitBtn) {
    submitBtn.addEventListener("click", function(e) {
      e.preventDefault();
      handleSecurityReviewSubmit();
    });
  }

  console.log("Security review modal handlers initialized");
}

// Retry functions for error recovery
window.retrySecurityReviewSubmit = function () {
  console.log("Retrying security review submission");
  hideModalError();
  handleSecurityReviewSubmit();
};

// Export for browser environment
if (typeof window !== "undefined") {
  window.showSecurityReviewModal = showSecurityReviewModal;
  window.handleSecurityReviewSubmit = handleSecurityReviewSubmit;
  window.validateSecurityReviewForm = validateSecurityReviewForm;
  window.resetFormValidation = resetFormValidation;
  window.showModalError = showModalError;
  window.hideModalError = hideModalError;
  window.resetSubmitButtonState = resetSubmitButtonState;
  window.showSubmitButtonLoadingState = showSubmitButtonLoadingState;
  window.getCurrentButtonState = getCurrentButtonState;
  window.initSecurityActionButtons = initSecurityActionButtons;
  window.initRealTimeFormValidation = initRealTimeFormValidation;
  window.initSecurityReviewModal = initSecurityReviewModal;
  window.BUTTON_STATE = BUTTON_STATE;
}

// Export for module environment
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    showSecurityReviewModal,
    handleSecurityReviewSubmit,
    validateSecurityReviewForm,
    resetFormValidation,
    showModalError,
    hideModalError,
    resetSubmitButtonState,
    showSubmitButtonLoadingState,
    getCurrentButtonState,
    initSecurityActionButtons,
    initRealTimeFormValidation,
    initSecurityReviewModal,
    BUTTON_STATE
  };
}
