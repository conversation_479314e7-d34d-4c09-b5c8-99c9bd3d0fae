#!/usr/bin/env python3
"""
Build script for ApiEndpointExplorer_v3
This script creates a standalone version of the application by embedding
all component files directly into the main HTML file.
"""

import os
import re

def read_file(file_path):
    """Read a file and return its content."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"Warning: File not found: {file_path}")
        return ""

def build_standalone():
    """Create a standalone HTML file with all components embedded."""
    
    # Read the main HTML file
    html_content = read_file('index.html')
    if not html_content:
        print("Error: Could not read index.html")
        return False
    
    # Read all component files
    components = {
        'left-menu': read_file('components/left-menu.html'),
        'top-menu': read_file('components/top-menu.html'),
        'main-content': read_file('components/main-content.html'),
        'detail-view': read_file('components/detail-view.html'),
        'help-modal': read_file('components/help-modal.html'),
        'security-review-modal': read_file('components/security-review-modal.html'),
        'security-review-history-modal': read_file('components/security-review-history-modal.html'),
        'advanced-search-modal': read_file('components/advanced-search-modal.html')
    }
    
    # Insert components into the HTML
    # Replace the comment placeholders with actual content
    # Note: The left menu should be inserted first in app-container to maintain proper order
    replacements = [
        ('<!-- Left Menu - Will be loaded dynamically -->', components['left-menu']),
        ('<!-- Top Menu - Will be loaded dynamically -->', components['top-menu']),
        ('<!-- Main Content (Grid) - Will be loaded dynamically -->', components['main-content']),
        ('<!-- Detail View - Will be loaded dynamically -->', components['detail-view']),
        ('<!-- All Modals will be loaded dynamically and appended to body -->', 
         f"{components['help-modal']}\n{components['security-review-modal']}\n{components['security-review-history-modal']}\n{components['advanced-search-modal']}")
    ]
    
    for placeholder, content in replacements:
        html_content = html_content.replace(placeholder, content)
    
    # Remove the component-loader.js script since we won't need it
    html_content = re.sub(r'<script src="js/component-loader\.js"></script>\s*', '', html_content)
    
    # Remove the initializeComponents() call and replace with direct initialization
    init_script = '''
    <script>
      // Initialize application when DOM is ready (components are now inline)
      document.addEventListener('DOMContentLoaded', function() {
        // Simulate components loaded event for compatibility
        document.dispatchEvent(new CustomEvent('componentsLoaded', {
          detail: { loadedComponents: ['embedded'] }
        }));
      });
    </script>'''
    
    # Replace the initialization script
    old_init = '''<script>
      // Initialize components when DOM is ready
      document.addEventListener('DOMContentLoaded', function() {
        // Load all components first
        initializeComponents();
      });
    </script>'''
    
    html_content = html_content.replace(old_init, init_script)
    
    # Write the standalone file
    output_file = 'index-standalone.html'
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"Successfully created {output_file}")
        print("You can now open this file directly in your browser without a server.")
        return True
    except Exception as e:
        print(f"Error writing {output_file}: {e}")
        return False

if __name__ == "__main__":
    print("Building standalone version of ApiEndpointExplorer_v3...")
    build_standalone()
