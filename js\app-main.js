// Main Application Module
// Handles initialization, event listeners, and overall application state

// Constants
const BASE_FONT_SIZE = 14.0; // Base font size in pixels

// Initialize the application when DOM is loaded AND components are loaded
document.addEventListener("DOMContentLoaded", () => {
  // Check if components are already loaded
  if (window.ComponentLoader && window.ComponentLoader.getLoadedComponents().length > 0) {
    initializeApp();
  } else {
    // Wait for components to be loaded
    document.addEventListener('componentsLoaded', initializeApp);
  }
});

// Main application initialization function
function initializeApp() {
  // Initialize event listeners
  initEventListeners();

  // Initialize advanced security search functionality
  if (typeof initSearchEventListeners === 'function') {
    initSearchEventListeners();
  }
  if (typeof initSearchFormHandlers === 'function') {
    initSearchFormHandlers();
  }
  if (typeof initResultsTableHandlers === 'function') {
    initResultsTableHandlers();
  }

  // Load the default data file automatically
  if (window.DataLoader) {
    window.DataLoader.loadDefaultData();
  } 

  // Initialize resizable elements
  initResizableElements();

  // Initialize sortable columns
  initSortableColumns();

  // Initialize burger menu
  initBurgerMenu();

  // Initialize property sheet pinning
  initPropertySheetPinning();

  // Initialize compact view toggle
  initCompactViewToggle();

  // Initialize tooltip toggle
  initTooltipToggle();

  // Initialize column resizing
  initTableColumnResizing();

  // Initialize row count display
  const rowCountSpan = document.getElementById("row-count");
  if (rowCountSpan) {
    rowCountSpan.textContent = "[0 rows]";
  }

  // Initialize font size based on stored state
  const currentFontSize = AppState.getCurrentFontSize();
  if (currentFontSize !== 1.0) {
    const newFontSize = BASE_FONT_SIZE * currentFontSize;
    document.documentElement.style.fontSize = `${newFontSize}px`;
    document.documentElement.style.setProperty("--font-size-multiplier", currentFontSize);
  } else {
    // Set the smaller base font size even at 1.0 multiplier
    document.documentElement.style.fontSize = `${BASE_FONT_SIZE}px`;
  }

  // Display initial "No data loaded" message
  if (window.GridManager) {
    window.GridManager.renderGrid();
  }

  // Add window resize handler with debouncing
  let resizeTimeout;
  window.addEventListener("resize", () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      if (window.LayoutManager) {
        window.LayoutManager.updateMainAreaWidth();
      }
    }, 100);
  });
}

// Initialize all event listeners
function initEventListeners() {
  // Filters
  const searchInput = document.getElementById("search-input");
  searchInput.addEventListener("input", () => {
    if (window.FiltersSortManager) {
      window.FiltersSortManager.applyFilters();
    }
  });

  const filterCheckboxes = document.querySelectorAll(
    '.checkbox-group input[type="checkbox"]'
  );
  filterCheckboxes.forEach((checkbox) => {
    checkbox.addEventListener("change", () => {
      if (window.FiltersSortManager) {
        window.FiltersSortManager.applyFilters();
      }
    });
  });

  // Add double-click event listeners to filter group headings
  const filterHeadings = document.querySelectorAll(".filter-group h3");
  filterHeadings.forEach((heading) => {
    const headingText = heading.textContent.trim();
    if (
      headingText === "Diff Type" ||
      headingText === "HTTP Verb" ||
      headingText === "Security Status" ||
      headingText === "Security Reviews" ||
      headingText === "Include in Api Client"
    ) {
      heading.addEventListener("dblclick", () => {
        if (window.NotificationManager) {
          window.NotificationManager.toggleAllCheckboxesInGroup(headingText);
        }
      });
      heading.style.cursor = "pointer"; // Show pointer cursor to indicate it's clickable
      // Add title attribute to indicate the functionality
      heading.title = "Double-click to toggle all checkboxes";
    }
  });

  // Policy radio buttons
  const policyRadios = document.querySelectorAll(
    '.radio-group input[type="radio"]'
  );
  policyRadios.forEach((radio) => {
    radio.addEventListener("change", () => {
      if (window.FiltersSortManager) {
        window.FiltersSortManager.applyFilters();
      }
    });
  });

  // Detail view
  const closeDetailBtn = document.getElementById("close-detail");
  closeDetailBtn.addEventListener("click", () => {
    if (window.DetailView) {
      window.DetailView.closeDetailView();
    }
  });

  // Help button
  const helpBtn = document.getElementById("help-btn");
  helpBtn.addEventListener("click", () => {
    if (window.NotificationManager) {
      window.NotificationManager.showHelp();
    }
  });

  // Font size controls
  const decreaseFontBtn = document.getElementById("decrease-font-btn");
  const resetFontBtn = document.getElementById("reset-font-btn");
  const increaseFontBtn = document.getElementById("increase-font-btn");

  console.log("Font buttons found:", {
    decrease: !!decreaseFontBtn,
    reset: !!resetFontBtn,
    increase: !!increaseFontBtn
  });

  if (decreaseFontBtn) {
    decreaseFontBtn.addEventListener("click", () => {
      console.log("Decrease font button clicked");
      changeFontSize(-0.1);
    });
  }
  if (resetFontBtn) {
    resetFontBtn.addEventListener("click", () => {
      console.log("Reset font button clicked");
      resetFontSize();
    });
  }
  if (increaseFontBtn) {
    increaseFontBtn.addEventListener("click", () => {
      console.log("Increase font button clicked");
      changeFontSize(0.1);
    });
  }

  // Compact view toggle
  const toggleCompactViewBtn = document.getElementById("toggle-compact-view");
  if (toggleCompactViewBtn) {
    toggleCompactViewBtn.addEventListener("click", toggleCompactView);
  }

  // Column Resize Controls
  const resetColumnsBtn = document.getElementById("reset-columns-btn");
  if (resetColumnsBtn) {
    resetColumnsBtn.addEventListener("click", () => {
      if (window.ColumnResizer) {
        window.ColumnResizer.resetColumnWidthsToDefaults();
      }
    });
  }

  const toggleTextWrapBtn = document.getElementById("toggle-text-wrap-btn");
  if (toggleTextWrapBtn) {
    toggleTextWrapBtn.addEventListener("click", () => {
      if (window.ColumnResizer) {
        window.ColumnResizer.toggleGridTextWrap();
      }
    });
  }

  // Tooltip toggle
  const toggleTooltipsBtn = document.getElementById("toggle-tooltips-btn");
  if (toggleTooltipsBtn) {
    toggleTooltipsBtn.addEventListener("click", toggleTooltips);
    updateTooltipButton(); // Ensure button is correct on load
  }
}

// Initialize burger menu
function initBurgerMenu() {
  const burgerMenuToggle = document.getElementById("burger-menu-toggle");
  const leftMenu = document.getElementById("left-menu");

  if (!burgerMenuToggle || !leftMenu) return;

  // Set initial state based on window size
  if (window.innerWidth < 768) {
    toggleSidebar(true); // Collapse on small screens by default
  } else {
    // Make sure the toggle button position reflects the initial state
    updateBurgerTogglePosition();
  }

  burgerMenuToggle.addEventListener("click", () => {
    toggleSidebar();
  });

  // Update burger icon based on sidebar state
  updateBurgerIcon();
}

// Toggle sidebar visibility
function toggleSidebar(forceCollapse = null) {
  const leftMenu = document.getElementById("left-menu");
  const burgerMenuToggle = document.getElementById("burger-menu-toggle");
  const mainArea = document.querySelector(".main-area");
  const appContainer = document.querySelector(".app-container");

  if (!leftMenu || !burgerMenuToggle || !mainArea || !appContainer) return;

  // If forceCollapse is provided, use that value, otherwise toggle current state
  const wasCollapsed = AppState.isSidebarCollapsed();
  const newCollapsedState = forceCollapse !== null ? forceCollapse : !wasCollapsed;
  AppState.setSidebarCollapsed(newCollapsedState);

  if (newCollapsedState) {
    // Store the current width before collapsing
    if (!wasCollapsed) {
      const currentWidth = parseInt(getComputedStyle(leftMenu).width, 10) || 250;
      AppState.setLastSidebarWidth(currentWidth);
    }
    leftMenu.style.width = ""; // Clear inline style to allow CSS .collapsed to set width to 0

    // Add collapsed classes
    leftMenu.classList.add("collapsed");
    burgerMenuToggle.classList.remove("sidebar-visible");
    appContainer.classList.add("sidebar-collapsed");

    // Update CSS variable for left menu width
    document.documentElement.style.setProperty("--left-menu-width", "0px");
  } else {
    // Remove collapsed classes
    leftMenu.classList.remove("collapsed");
    burgerMenuToggle.classList.add("sidebar-visible");
    appContainer.classList.remove("sidebar-collapsed");

    // Restore the previous width if available
    const lastWidth = AppState.getLastSidebarWidth();
    if (lastWidth) {
      leftMenu.style.width = `${lastWidth}px`;

      // Update CSS variable with the restored width
      document.documentElement.style.setProperty(
        "--left-menu-width",
        `${lastWidth}px`
      );
    } else {
      // If no stored width, use the default
      const leftMenuWidth =
        parseInt(getComputedStyle(leftMenu).width, 10) || 250;
      document.documentElement.style.setProperty(
        "--left-menu-width",
        `${leftMenuWidth}px`
      );
    }
  }

  // Update burger icon
  updateBurgerIcon();

  // Update main area width based on new sidebar state
  if (window.LayoutManager) {
    window.LayoutManager.updateMainAreaWidth();
  }
}

// Update burger icon based on sidebar state
function updateBurgerIcon() {
  const burgerMenuToggle = document.getElementById("burger-menu-toggle");

  if (!burgerMenuToggle) return;

  // Change icon based on state
  burgerMenuToggle.innerHTML = AppState.isSidebarCollapsed() ? "☰" : "×";
}

// Update burger toggle position based on sidebar state
function updateBurgerTogglePosition() {
  const burgerMenuToggle = document.getElementById("burger-menu-toggle");

  if (!burgerMenuToggle) return;

  if (AppState.isSidebarCollapsed()) {
    burgerMenuToggle.classList.remove("sidebar-visible");
  } else {
    burgerMenuToggle.classList.add("sidebar-visible");
  }
}

// Initialize property sheet pinning and grid view toggle
function initPropertySheetPinning() {
  const pinDetailBtn = document.getElementById("pin-detail");
  const closeDetailBtn = document.getElementById("close-detail");
  const gridViewToggle = document.getElementById("grid-view-toggle");

  if (pinDetailBtn) {
    pinDetailBtn.addEventListener("click", togglePropertySheetPin);
    // Initialize pin button icon and close button visibility based on current state
    if (window.LayoutManager) {
      window.LayoutManager.updatePinButtonIcon();
    }
  }

  if (closeDetailBtn) {
    closeDetailBtn.addEventListener("click", () => {
      if (window.DetailView) {
        window.DetailView.closeDetailView();
      }
    });
  }

  // Initialize grid view toggle if it exists
  if (gridViewToggle) {
    gridViewToggle.addEventListener("change", () => {
      // If we have a current endpoint, refresh the detail view to apply the grid view
      const currentEndpoint = AppState.getCurrentEndpoint();
      if (currentEndpoint && window.DetailView) {
        window.DetailView.showDetailView(currentEndpoint);
      }
    });
  }
}

// Toggle property sheet pin state
function togglePropertySheetPin() {
  const detailView = document.getElementById("detail-view");
  const pinDetailBtn = document.getElementById("pin-detail");
  const appContainer = document.querySelector(".app-container");

  if (!detailView || !pinDetailBtn) return;

  const newPinnedState = !AppState.isDetailPinned();
  AppState.setDetailPinned(newPinnedState);

  if (newPinnedState) {
    // Pin the property sheet to the right side
    detailView.classList.add("pinned");

    // Add pinned-layout class to app container
    if (appContainer) {
      appContainer.classList.add("pinned-layout");
    }

    // Ensure the detail view is visible
    detailView.classList.add("active");

    // Update CSS variable for detail view width
    const detailViewWidth = getComputedStyle(detailView).width;
    document.documentElement.style.setProperty(
      "--detail-view-width",
      detailViewWidth
    );

    // Make sure the property sheet is positioned correctly
    detailView.style.top = "0";
    detailView.style.position = "relative";
  } else {
    // Unpin the property sheet
    detailView.classList.remove("pinned");

    // Remove pinned-layout class from app container
    if (appContainer) {
      appContainer.classList.remove("pinned-layout");
    }

    // Reset positioning for floating mode
    detailView.style.position = "fixed";
    detailView.style.top = "0";
    detailView.style.right = "0";
  }

  // Update pin button icon and close button visibility
  if (window.LayoutManager) {
    window.LayoutManager.updatePinButtonIcon();
  }

  // Update main area width after pin state change
  if (window.LayoutManager) {
    window.LayoutManager.updateMainAreaWidth();
  }
}

// Initialize compact view toggle
function initCompactViewToggle() {
  const toggleCompactViewBtn = document.getElementById("toggle-compact-view");

  if (!toggleCompactViewBtn) return;

  // Update button text based on initial state
  updateCompactViewButton();
}

// Toggle compact view
function toggleCompactView() {
  const newCompactState = !AppState.isCompactViewEnabled();
  AppState.setCompactViewEnabled(newCompactState);

  // Apply compact view to grid
  const grid = document.getElementById("endpoints-grid");
  if (grid) {
    if (newCompactState) {
      grid.classList.add("compact");
    } else {
      grid.classList.remove("compact");
    }
  }

  // Update button text
  updateCompactViewButton();
}

// Update compact view button text
function updateCompactViewButton() {
  const toggleCompactViewBtn = document.getElementById("toggle-compact-view");

  if (!toggleCompactViewBtn) return;

  const isCompact = AppState.isCompactViewEnabled();
  toggleCompactViewBtn.textContent = isCompact
    ? "Standard View"
    : "Compact View";
  toggleCompactViewBtn.title = isCompact
    ? "Switch to standard view"
    : "Switch to compact view";

  if (isCompact) {
    toggleCompactViewBtn.classList.add("active");
  } else {
    toggleCompactViewBtn.classList.remove("active");
  }
}

// Initialize tooltip toggle
function initTooltipToggle() {
  const toggleTooltipsBtn = document.getElementById("toggle-tooltips-btn");
  if (!toggleTooltipsBtn) return;

  // Set initial state of the button
  updateTooltipButton();
}

// Toggle tooltips
function toggleTooltips() {
  const newTooltipState = !AppState.areTooltipsEnabled();
  AppState.setTooltipsEnabled(newTooltipState);
  updateTooltipButton();
  
  // If tooltips are now disabled, hide any visible tooltip
  if (!newTooltipState && window.GridManager) {
    window.GridManager.hideTooltip();
  }
}

// Update tooltip button text and title
function updateTooltipButton() {
  const toggleTooltipsBtn = document.getElementById("toggle-tooltips-btn");
  if (toggleTooltipsBtn) {
    const tooltipsEnabled = AppState.areTooltipsEnabled();
    toggleTooltipsBtn.textContent = tooltipsEnabled
      ? "Hide Tooltips"
      : "Show Tooltips";
    toggleTooltipsBtn.title = tooltipsEnabled
      ? "Hide tooltips"
      : "Show tooltips";
    if (tooltipsEnabled) {
      toggleTooltipsBtn.classList.add("active");
    } else {
      toggleTooltipsBtn.classList.remove("active");
    }
  }
}

// Initialize resizable elements
function initResizableElements() {
  // Left menu resizer
  if (window.LayoutManager) {
    window.LayoutManager.initLeftMenuResizer();
  }

  // Detail view resizer
  if (window.LayoutManager) {
    window.LayoutManager.initDetailViewResizer();
  }
}

// Initialize sortable columns
function initSortableColumns() {
  if (window.FiltersSortManager) {
    window.FiltersSortManager.initSortableColumns();
  }
}

// Initialize table column resizing
function initTableColumnResizing() {
  if (window.ColumnResizer) {
    window.ColumnResizer.initTableColumnResizing();
  }
}

// Font size management
function changeFontSize(delta) {
  const currentSize = AppState.getCurrentFontSize();
  const newSize = Math.max(0.5, Math.min(3.0, currentSize + delta));
  AppState.setCurrentFontSize(newSize);
  
  console.log(`Changing font size from ${currentSize} to ${newSize}`);
  
  // Apply font scaling using base font size constant
  const newFontSize = BASE_FONT_SIZE * newSize;
  document.documentElement.style.fontSize = `${newFontSize}px`;
  
  // Also update the CSS variable for compatibility
  document.documentElement.style.setProperty("--font-size-multiplier", newSize);
}

function resetFontSize() {
  AppState.setCurrentFontSize(1.0);
  
  console.log("Resetting font size to 1.0");
  
  // Reset to base font size
  document.documentElement.style.fontSize = `${BASE_FONT_SIZE}px`;
  
  // Also update the CSS variable
  document.documentElement.style.setProperty("--font-size-multiplier", "1.0");
}

// Export main application functions
window.AppMain = {
  initEventListeners,
  initBurgerMenu,
  toggleSidebar,
  updateBurgerIcon,
  updateBurgerTogglePosition,
  initPropertySheetPinning,
  togglePropertySheetPin,
  initCompactViewToggle,
  toggleCompactView,
  updateCompactViewButton,
  initTooltipToggle,
  toggleTooltips,
  updateTooltipButton,
  initResizableElements,
  initSortableColumns,
  initTableColumnResizing,
  changeFontSize,
  resetFontSize
};

// Make key functions available globally for backward compatibility
window.toggleSidebar = toggleSidebar;
window.togglePropertySheetPin = togglePropertySheetPin;
window.toggleCompactView = toggleCompactView;
window.toggleTooltips = toggleTooltips;
window.changeFontSize = changeFontSize;
window.resetFontSize = resetFontSize;
