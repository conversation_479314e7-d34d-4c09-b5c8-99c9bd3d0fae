// Notifications and Error Handling Module
// Handles toast notifications, error messages, and user feedback

// Enhanced Error Handling and User Feedback Functions

// Show error message with improved user experience
function showError(message, options = {}) {
  console.error("Application Error:", message);

  const {
    persistent = true,
    showInGrid = true,
    showToast = true,
    allowRetry = false,
    retryCallback = null,
    details = null,
  } = options;

  // Show toast notification for immediate feedback
  if (showToast) {
    showToastMessage(message, "error", {
      persistent: persistent,
      allowRetry: allowRetry,
      retryCallback: retryCallback,
    });
  }

  // Also display the error in the grid body for persistent visibility
  if (showInGrid) {
    const gridBody = document.getElementById("grid-body");
    if (gridBody) {
      const columnCount =
        document.querySelectorAll("#endpoints-grid thead th").length || 8;

      let errorContent = `
        <tr>
          <td colspan="${columnCount}" class="text-center text-danger p-4">
            <div class="alert alert-danger" role="alert">
              <div class="d-flex align-items-center justify-content-center mb-2">
                <i class="bi bi-exclamation-triangle-fill me-2" style="font-size: 1.5rem;"></i>
                <h5 class="mb-0">Error</h5>
              </div>
              <p class="mb-2">${escapeHtml(message)}</p>`;

      if (details) {
        errorContent += `
              <details class="mt-2">
                <summary class="btn btn-sm btn-outline-danger">Show Details</summary>
                <div class="mt-2 p-2 bg-light border rounded">
                  <small class="text-muted">${escapeHtml(details)}</small>
                </div>
              </details>`;
      }

      if (allowRetry && retryCallback) {
        errorContent += `
              <div class="mt-3">
                <button class="btn btn-outline-primary btn-sm" onclick="handleRetry('${retryCallback}')">
                  <i class="bi bi-arrow-clockwise me-1"></i>Retry
                </button>
              </div>`;
      }

      errorContent += `
              <hr>
              <small class="text-muted">
                <i class="bi bi-info-circle me-1"></i>
                Check the browser console (F12) for technical details.
              </small>
            </div>
          </td>
        </tr>`;

      gridBody.innerHTML = errorContent;
    }
  }
}

// Show success message
function showSuccess(message, options = {}) {
  console.log("Success:", message);

  const { persistent = false, showToast = true } = options;

  if (showToast) {
    showToastMessage(message, "success", { persistent });
  }
}

// Show warning message
function showWarning(message, options = {}) {
  console.warn("Warning:", message);

  const { persistent = false, showToast = true } = options;

  if (showToast) {
    showToastMessage(message, "warning", { persistent });
  }
}

// Show info message
function showInfo(message, options = {}) {
  console.info("Info:", message);

  const { persistent = false, showToast = true } = options;

  if (showToast) {
    showToastMessage(message, "info", { persistent });
  }
}

// Enhanced toast notification system
function showToastMessage(message, type = "info", options = {}) {
  const {
    persistent = false,
    duration = persistent ? 0 : type === "error" ? 8000 : 4000,
    allowRetry = false,
    retryCallback = null,
    position = "top-end",
  } = options;

  // Create toast container if it doesn't exist
  let toastContainer = document.getElementById("toast-container");
  if (!toastContainer) {
    toastContainer = document.createElement("div");
    toastContainer.id = "toast-container";
    toastContainer.className = `toast-container position-fixed ${position} p-3`;
    toastContainer.style.zIndex = "9999";
    document.body.appendChild(toastContainer);
  }

  // Create toast element
  const toastId = `toast-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}`;
  const toast = document.createElement("div");
  toast.id = toastId;
  toast.className = "toast";
  toast.setAttribute("role", persistent ? "alert" : "status");
  toast.setAttribute("aria-live", "assertive");
  toast.setAttribute("aria-atomic", "true");

  if (!persistent && duration > 0) {
    toast.setAttribute("data-bs-autohide", "true");
    toast.setAttribute("data-bs-delay", duration.toString());
  } else {
    toast.setAttribute("data-bs-autohide", "false");
  }

  // Determine toast styling based on type
  const typeConfig = {
    error: {
      bgClass: "bg-danger",
      textClass: "text-white",
      icon: "bi-exclamation-triangle-fill",
      title: "Error",
    },
    success: {
      bgClass: "bg-success",
      textClass: "text-white",
      icon: "bi-check-circle-fill",
      title: "Success",
    },
    warning: {
      bgClass: "bg-warning",
      textClass: "text-dark",
      icon: "bi-exclamation-triangle-fill",
      title: "Warning",
    },
    info: {
      bgClass: "bg-info",
      textClass: "text-white",
      icon: "bi-info-circle-fill",
      title: "Information",
    },
  };

  const config = typeConfig[type] || typeConfig.info;

  let toastContent = `
    <div class="toast-header ${config.bgClass} ${config.textClass}">
      <i class="bi ${config.icon} me-2"></i>
      <strong class="me-auto">${config.title}</strong>
      <small class="text-muted">${new Date().toLocaleTimeString()}</small>
      <button type="button" class="btn-close${
        config.textClass.includes("white") ? " btn-close-white" : ""
      }" 
              data-bs-dismiss="toast" aria-label="Close"></button>
    </div>
    <div class="toast-body ${
      config.textClass.includes("white") ? "text-dark" : ""
    }">
      ${escapeHtml(message)}`;

  if (allowRetry && retryCallback) {
    toastContent += `
      <div class="mt-2">
        <button class="btn btn-sm btn-outline-primary" onclick="handleRetry('${retryCallback}'); bootstrap.Toast.getInstance(document.getElementById('${toastId}')).hide();">
          <i class="bi bi-arrow-clockwise me-1"></i>Retry
        </button>
      </div>`;
  }

  toastContent += `</div>`;
  toast.innerHTML = toastContent;

  // Add to container
  toastContainer.appendChild(toast);

  // Initialize Bootstrap toast
  const bsToast = new bootstrap.Toast(toast);

  // Show the toast
  bsToast.show();

  // Clean up after toast is hidden
  toast.addEventListener("hidden.bs.toast", () => {
    toast.remove();
  });

  return toastId;
}

// Handle retry operations
function handleRetry(callbackName) {
  try {
    if (typeof window[callbackName] === "function") {
      window[callbackName]();
    } else {
      console.error(`Retry callback '${callbackName}' not found`);
      showError("Retry function not available");
    }
  } catch (error) {
    console.error("Error during retry:", error);
    showError(`Retry failed: ${error.message}`);
  }
}

// Show loading state
function showLoadingState(element, message = "Loading...") {
  if (!element) return null;

  // Store original content
  const originalContent = element.innerHTML;
  const originalDisabled = element.disabled;

  // Create loading content
  const loadingContent = `
    <div class="d-flex align-items-center justify-content-center">
      <div class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></div>
      <span>${escapeHtml(message)}</span>
    </div>`;

  // Apply loading state
  element.innerHTML = loadingContent;
  element.disabled = true;
  element.classList.add("loading-state");

  // Return function to hide loading state
  return function hideLoading() {
    element.innerHTML = originalContent;
    element.disabled = originalDisabled;
    element.classList.remove("loading-state");
  };
}

// Show loading overlay for containers
function showLoadingOverlay(container, message = "Loading...") {
  if (!container) return null;

  const overlay = document.createElement("div");
  overlay.className =
    "loading-overlay position-absolute w-100 h-100 d-flex align-items-center justify-content-center";
  overlay.style.cssText = `
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 1000;
    backdrop-filter: blur(2px);
  `;

  overlay.innerHTML = `
    <div class="text-center">
      <div class="spinner-border text-primary mb-2" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <div class="text-muted">${escapeHtml(message)}</div>
    </div>`;

  // Make container relative if it's not already positioned
  const originalPosition = getComputedStyle(container).position;
  if (originalPosition === "static") {
    container.style.position = "relative";
  }

  container.appendChild(overlay);

  // Return function to hide overlay
  return function hideOverlay() {
    overlay.remove();
    if (originalPosition === "static") {
      container.style.position = "";
    }
  };
}

// Utility function to escape HTML
function escapeHtml(text) {
  if (typeof text !== "string") {
    text = String(text);
  }

  const div = document.createElement("div");
  div.textContent = text;
  return div.innerHTML;
}

// Enhanced validation error display
function showValidationErrors(errors, formElement = null) {
  if (!errors || errors.length === 0) return;

  // Show general error message
  const errorMessage = `Validation failed: ${errors.length} error${
    errors.length > 1 ? "s" : ""
  } found`;
  showError(errorMessage, {
    showInGrid: false,
    details: errors.join("\n"),
    persistent: true,
  });

  // If form element provided, highlight specific fields
  if (formElement) {
    // Clear previous validation states
    const invalidElements = formElement.querySelectorAll(".is-invalid");
    invalidElements.forEach((el) => {
      el.classList.remove("is-invalid");
      const feedback = el.parentElement.querySelector(".invalid-feedback");
      if (feedback) {
        feedback.textContent = "";
      }
    });

    // Apply validation errors to specific fields
    errors.forEach((error) => {
      const fieldName = extractFieldNameFromError(error);
      if (fieldName) {
        const field = formElement.querySelector(
          `[name="${fieldName}"], #${fieldName}`
        );
        if (field) {
          field.classList.add("is-invalid");
          const feedback =
            field.parentElement.querySelector(".invalid-feedback");
          if (feedback) {
            feedback.textContent = error;
          }
        }
      }
    });

    // Focus on first invalid field
    const firstInvalid = formElement.querySelector(".is-invalid");
    if (firstInvalid) {
      firstInvalid.focus();
    }
  }
}

// Extract field name from validation error message
function extractFieldNameFromError(error) {
  const fieldPatterns = [
    /^(\w+):/,
    /(\w+)\s+is\s+required/i,
    /(\w+)\s+must\s+be/i,
    /(\w+)\s+cannot\s+be/i,
  ];

  for (const pattern of fieldPatterns) {
    const match = error.match(pattern);
    if (match) {
      return match[1].toLowerCase();
    }
  }

  return null;
}

// Success message specifically for security reviews
function showSuccessMessage(message) {
  showSuccess(message, { persistent: false });
}

// Show help modal
function showHelp() {
  const helpHtmlContent = `
        <div class="help-content">
            <div class="alert alert-warning" role="alert">
                <strong>⚠️ SECURITY DISCLAIMER</strong><br>
                This tool helps Security teams and developers identify changes in API endpoint signatures, but it is not a substitute for thorough security reviews.
                Any internal modifications can have severe security implications. Always ensure that changes to API endpoints are reviewed and tested for security implications.
            </div>
            
            <h4>📋 Application Overview</h4>
            <p>ApiEndpointExplorer_v3 is a comprehensive tool for analyzing and visualizing API endpoint changes between different versions. 
            It provides an interactive interface for loading, filtering, and comparing API endpoint definitions with a focus on identifying 
            what has been added, removed, or modified.</p>
            
            <h4>🚀 Getting Started</h4>
            <ol>
                <li><strong>Load Data</strong>: The application automatically loads sample data on startup. You can also click "Load File" 
                to select your own JSON file containing endpoint definitions.</li>
                <li><strong>Explore the Interface</strong>: Use the left sidebar for filtering, the main grid for data overview, 
                and the detail view for in-depth analysis.</li>
                <li><strong>Customize Your View</strong>: Adjust filters, resize panels, and toggle display options to suit your needs.</li>
            </ol>
            
            <h4>🔍 Search & Filtering</h4>
            <ul>
                <li><strong>Global Search</strong>: Type in the search bar to filter by any text across all endpoint properties 
                (route, policy, action name, request parameters, etc.).</li>
                <li><strong>Diff Type Filters</strong>: Show/hide endpoints by change status:
                    <ul>
                        <li>✅ <strong>Added</strong>: New endpoints in the current version</li>
                        <li>❌ <strong>Removed</strong>: Endpoints that were deleted</li>
                        <li>📝 <strong>Modified</strong>: Endpoints with changes to parameters, policy, or other attributes</li>
                        <li>⚪ <strong>Unmodified</strong>: Endpoints that remained the same</li>
                    </ul>
                </li>
                <li><strong>HTTP Method Filters</strong>: Filter by GET, POST, PUT, DELETE, and other HTTP verbs</li>
                <li><strong>Security Policy Filters</strong>: Filter by authorization policies (AllowAnonymous, Default, Custom policies)</li>
                <li><strong>Security Review Status</strong>: Filter by review completion status and security ratings</li>
                <li><strong>API Client Inclusion</strong>: Show endpoints included/excluded from client code generation</li>
                <li><strong>Quick Toggles</strong>: Double-click any filter group heading to toggle all options in that group</li>
            </ul>
            
            <h4>📊 Grid Features</h4>
            <ul>
                <li><strong>Color Coding</strong>:
                    <ul>
                        <li><span style="background-color: rgba(40, 167, 69, 0.3); padding: 2px 6px; border-radius: 3px; color: #155724;">🟢 Added</span></li>
                        <li><span style="background-color: rgba(220, 53, 69, 0.3); padding: 2px 6px; border-radius: 3px; color: #721c24;">🔴 Removed</span></li>
                        <li><span style="background-color: rgba(255, 193, 7, 0.3); padding: 2px 6px; border-radius: 3px; color: #856404;">🟡 Modified</span></li>
                        <li><span style="background-color: rgba(108, 117, 125, 0.1); padding: 2px 6px; border-radius: 3px;">⚪ Unmodified</span></li>
                    </ul>
                </li>
                <li><strong>Sorting</strong>: Click column headers to sort. Click again to reverse order, and once more to remove sorting.</li>
                <li><strong>Column Resizing</strong>: Drag the vertical dividers between headers to resize columns. Double-click to auto-fit.</li>
                <li><strong>Tooltips</strong>: Hover over rows for quick endpoint information. Toggle tooltips on/off with the "Show Tooltips" button.</li>
                <li><strong>Row Selection</strong>: Click any row to view detailed information in the Detail View panel.</li>
            </ul>
            
            <h4>🔧 Security Review System</h4>
            <ul>
                <li><strong>Security Reviews</strong>: Conduct structured security assessments for individual endpoints</li>
                <li><strong>Review Status Tracking</strong>: Monitor which endpoints have been reviewed and their security ratings</li>
                <li><strong>Review History</strong>: Access complete audit trails of all security reviews with timestamps and reviewers</li>
                <li><strong>Advanced Search</strong>: Use the advanced search modal to find endpoints by complex security criteria</li>
                <li><strong>Bulk Operations</strong>: Perform mass security review operations on multiple endpoints</li>
                <li><strong>Data Persistence</strong>: Reviews are automatically saved and can be exported or integrated with backend systems</li>
            </ul>
            
            <h4>📱 Detail View</h4>
            <ul>
                <li><strong>Comprehensive Information</strong>: View complete endpoint details including parameters, responses, and metadata</li>
                <li><strong>Change Comparison</strong>: For modified endpoints, see side-by-side old vs. new comparisons</li>
                <li><strong>Pinning</strong>: Click the pin icon (📌) to dock the Detail View. Click the unpin icon (📍) to make it floating.</li>
                <li><strong>View Modes</strong>: Toggle between Grid and Standard layouts for different information presentation styles</li>
                <li><strong>Security Information</strong>: View security review status, ratings, and comments directly in the detail panel</li>
            </ul>
            
            <h4>⚙️ Customization & Layout</h4>
            <ul>
                <li><strong>Sidebar Toggle</strong>: Use the hamburger menu (☰) to show/hide the left filter panel</li>
                <li><strong>Panel Resizing</strong>: Drag panel edges to customize the workspace layout</li>
                <li><strong>Font Size Control</strong>: Use A-/A/A+ buttons to adjust text size throughout the application</li>
                <li><strong>Compact View</strong>: Toggle condensed grid layout for viewing more data at once</li>
                <li><strong>Text Wrapping</strong>: Control how long text in cells is displayed (wrapped or truncated)</li>
                <li><strong>Column Reset</strong>: Restore default column widths with the "Reset Columns" button</li>
            </ul>
            
            <h4>💾 Data Management</h4>
            <ul>
                <li><strong>File Loading</strong>: Support for JSON files with endpoint diff data</li>
                <li><strong>Default Dataset</strong>: Sample data automatically loaded for demonstration</li>
                <li><strong>Export Options</strong>: Generate standalone HTML files or export filtered data</li>
                <li><strong>Backend Integration</strong>: Optional .NET Core API for persistent security review data</li>
            </ul>
            
            <h4>⌨️ Keyboard Shortcuts & Accessibility</h4>
            <ul>
                <li><strong>Keyboard Navigation</strong>: Full keyboard support for all interface elements</li>
                <li><strong>Screen Reader Friendly</strong>: ARIA labels and roles for accessibility compliance</li>
                <li><strong>Focus Management</strong>: Logical tab order and visible focus indicators</li>
                <li><strong>Right-Click Actions</strong>: Context menus and alternative access methods</li>
            </ul>
            
            <h4>📈 Status Information</h4>
            <p>The application displays real-time information about your current view:</p>
            <ul>
                <li><strong>Row Count</strong>: Shows the number of endpoints currently visible after filtering (displayed in brackets next to the title)</li>
                <li><strong>Dataset Summary</strong>: View total counts of added, removed, modified, and unmodified endpoints</li>
                <li><strong>Filter Status</strong>: Active filters are clearly indicated in the interface</li>
            </ul>
            
            <h4>🔗 Integration & Deployment</h4>
            <ul>
                <li><strong>Standalone Mode</strong>: Generate self-contained HTML files that work without a server</li>
                <li><strong>Server Mode</strong>: Run with HTTP server for full component loading and backend integration</li>
                <li><strong>API Integration</strong>: Connect to external APIs for data loading and security review persistence</li>
                <li><strong>CI/CD Ready</strong>: Suitable for integration into continuous integration and deployment pipelines</li>
            </ul>
            
            <div class="alert alert-info mt-3" role="alert">
                <strong>💡 Pro Tip:</strong> This application is designed to help you quickly understand API changes and their security implications. 
                Use the filtering and search capabilities to focus on the changes most relevant to your work, and leverage the security review 
                system to ensure all changes are properly assessed before deployment.
            </div>
        </div>
    `;

  const helpModalBody = document.getElementById("helpModalBody");
  if (helpModalBody) {
    helpModalBody.innerHTML = helpHtmlContent;
  }

  const helpModalElement = document.getElementById("helpModal");
  if (helpModalElement) {
    const modal = new bootstrap.Modal(helpModalElement);
    modal.show();
  }
}

// Toggle all checkboxes in a filter group
function toggleAllCheckboxesInGroup(groupName) {
  console.log(`Toggling all checkboxes in ${groupName} group`);

  // Find the filter group
  const filterGroups = document.querySelectorAll(".filter-group");
  let targetGroup = null;

  for (const group of filterGroups) {
    const heading = group.querySelector("h3");
    if (heading && heading.textContent.trim() === groupName) {
      targetGroup = group;
      break;
    }
  }

  if (!targetGroup) {
    console.warn(`Filter group ${groupName} not found`);
    return;
  }

  // Get all checkboxes in this group
  const checkboxes = targetGroup.querySelectorAll('input[type="checkbox"]');
  if (checkboxes.length === 0) {
    console.warn(`No checkboxes found in ${groupName} group`);
    return;
  }

  // Determine current state - if all are checked, uncheck all; otherwise, check all
  const allChecked = Array.from(checkboxes).every(
    (checkbox) => checkbox.checked
  );

  // Toggle all checkboxes
  checkboxes.forEach((checkbox) => {
    checkbox.checked = !allChecked;
  });

  // Apply filters to update the grid
  if (window.FiltersSortManager) {
    window.FiltersSortManager.applyFilters();
  }

  console.log(
    `${groupName} checkboxes toggled to ${
      !allChecked ? "checked" : "unchecked"
    }`
  );
}

// Retry callback functions for error recovery
window.retryFileLoad = function () {
  const fileInput = document.getElementById("file-input");
  if (fileInput && fileInput.files && fileInput.files.length > 0) {
    if (window.DataLoader) {
      window.DataLoader.loadDataFromFile(fileInput.files[0]);
    }
  } else {
    showWarning("No file selected. Please select a file to load.", {
      persistent: false,
    });
    // Trigger file input click
    fileInput.click();
  }
};

window.retryDefaultDataLoad = function () {
  if (window.DataLoader) {
    window.DataLoader.loadDefaultData();
  }
};

window.retryLoadSecurityReviews = function () {
  if (window.DataLoader) {
    window.DataLoader.loadSecurityReviewsFromFile();
  }
};

// Export notification manager for use by other modules
window.NotificationManager = {
  showError,
  showSuccess,
  showWarning,
  showInfo,
  showToastMessage,
  handleRetry,
  showLoadingState,
  showLoadingOverlay,
  escapeHtml,
  showValidationErrors,
  extractFieldNameFromError,
  showSuccessMessage,
  showHelp,
  toggleAllCheckboxesInGroup
};

// Make key functions available globally for backward compatibility
window.showError = showError;
window.showSuccess = showSuccess;
window.showWarning = showWarning;
window.showInfo = showInfo;
window.showToastMessage = showToastMessage;
window.handleRetry = handleRetry;
window.showLoadingState = showLoadingState;
window.showLoadingOverlay = showLoadingOverlay;
window.escapeHtml = escapeHtml;
window.showValidationErrors = showValidationErrors;
window.showSuccessMessage = showSuccessMessage;
window.showHelp = showHelp;
window.toggleAllCheckboxesInGroup = toggleAllCheckboxesInGroup;
