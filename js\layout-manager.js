// Layout and UI Management Module
// Handles sidebar, detail view, resizing, and general UI layout

// Layout state functions using AppState
function updateMainAreaWidth() {
  const mainArea = document.querySelector(".main-area");
  const detailView = document.getElementById("detail-view");

  if (!mainArea) return;

  // Use requestAnimationFrame to ensure DOM has updated
  requestAnimationFrame(() => {
    // Remove any existing inline styles to start fresh - let CSS flexbox handle the layout
    mainArea.style.width = "";
    mainArea.style.marginLeft = "";
    mainArea.style.marginRight = "";
    mainArea.style.position = "";
    mainArea.style.left = "";
    mainArea.style.right = "";

    const isDetailViewActive =
      detailView && (detailView.classList.contains("active") || AppState.isDetailPinned());
    const detailViewWidth = isDetailViewActive ? detailView.offsetWidth : 0;

    // Only set explicit width when detail view is pinned to ensure proper space allocation
    if (isDetailViewActive && AppState.isDetailPinned()) {
      if (AppState.isSidebarCollapsed()) {
        // Sidebar collapsed and detail view pinned
        mainArea.style.width = `calc(100% - ${detailViewWidth}px)`;
      } else {
        // Sidebar expanded and detail view pinned
        const leftMenu = document.getElementById("left-menu");
        const leftMenuWidth =
          parseInt(getComputedStyle(leftMenu).width, 10) || 250;
        mainArea.style.width = `calc(100% - ${leftMenuWidth}px - ${detailViewWidth}px)`;
      }
    }
    // For all other cases, let CSS flexbox handle the layout automatically
  });
}

// Initialize burger menu
function initBurgerMenu() {
  const burgerMenuToggle = document.getElementById("burger-menu-toggle");
  const leftMenu = document.getElementById("left-menu");

  if (!burgerMenuToggle || !leftMenu) return;

  // Set initial state based on window size
  if (window.innerWidth < 768) {
    toggleSidebar(true); // Collapse on small screens by default
  } else {
    // Make sure the toggle button position reflects the initial state
    updateBurgerTogglePosition();
  }

  burgerMenuToggle.addEventListener("click", () => {
    toggleSidebar();
  });

  // Update burger icon based on sidebar state
  updateBurgerIcon();
}

// Toggle sidebar visibility
function toggleSidebar(forceCollapse = null) {
  const leftMenu = document.getElementById("left-menu");
  const burgerMenuToggle = document.getElementById("burger-menu-toggle");
  const mainArea = document.querySelector(".main-area");
  const appContainer = document.querySelector(".app-container");

  if (!leftMenu || !burgerMenuToggle || !mainArea || !appContainer) return;

  // If forceCollapse is provided, use that value, otherwise toggle current state
  const wasCollapsed = AppState.isSidebarCollapsed();
  const newCollapsedState = forceCollapse !== null ? forceCollapse : !AppState.isSidebarCollapsed();
  AppState.setSidebarCollapsed(newCollapsedState);

  if (AppState.isSidebarCollapsed()) {
    // Store the current width before collapsing
    if (!wasCollapsed) {
      AppState.setLastSidebarWidth(parseInt(getComputedStyle(leftMenu).width, 10) || 250);
    }
    leftMenu.style.width = ""; // Clear inline style to allow CSS .collapsed to set width to 0

    // Add collapsed classes
    leftMenu.classList.add("collapsed");
    burgerMenuToggle.classList.remove("sidebar-visible");
    appContainer.classList.add("sidebar-collapsed");

    // Update CSS variable for left menu width
    document.documentElement.style.setProperty("--left-menu-width", "0px");
  } else {
    // Remove collapsed classes
    leftMenu.classList.remove("collapsed");
    burgerMenuToggle.classList.add("sidebar-visible");
    appContainer.classList.remove("sidebar-collapsed");

    // Restore the previous width if available
    const lastWidth = AppState.getLastSidebarWidth();
    if (lastWidth) {
      leftMenu.style.width = `${lastWidth}px`;

      // Update CSS variable with the restored width
      document.documentElement.style.setProperty(
        "--left-menu-width",
        `${lastWidth}px`
      );
    } else {
      // If no stored width, use the default
      const leftMenuWidth =
        parseInt(getComputedStyle(leftMenu).width, 10) || 250;
      document.documentElement.style.setProperty(
        "--left-menu-width",
        `${leftMenuWidth}px`
      );
    }
  }

  // Update burger icon
  updateBurgerIcon();

  // Update main area width based on new sidebar state
  updateMainAreaWidth();
}

// Update burger icon based on sidebar state
function updateBurgerIcon() {
  const burgerMenuToggle = document.getElementById("burger-menu-toggle");

  if (!burgerMenuToggle) return;

  // Change icon based on state
  burgerMenuToggle.innerHTML = AppState.isSidebarCollapsed() ? "☰" : "×";
}

// Update burger toggle position based on sidebar state
function updateBurgerTogglePosition() {
  const burgerMenuToggle = document.getElementById("burger-menu-toggle");

  if (!burgerMenuToggle) return;

  if (AppState.isSidebarCollapsed()) {
    burgerMenuToggle.classList.remove("sidebar-visible");
  } else {
    burgerMenuToggle.classList.add("sidebar-visible");
  }
}

// Initialize property sheet pinning and grid view toggle
function initPropertySheetPinning() {
  const pinDetailBtn = document.getElementById("pin-detail");
  const closeDetailBtn = document.getElementById("close-detail");
  const gridViewToggle = document.getElementById("grid-view-toggle");

  if (pinDetailBtn) {
    pinDetailBtn.addEventListener("click", togglePropertySheetPin);
    // Initialize pin button icon and close button visibility based on current state
    updatePinButtonIcon();
  }

  if (closeDetailBtn) {
    closeDetailBtn.addEventListener("click", () => {
      if (window.DetailView) {
        window.DetailView.closeDetailView();
      }
    });
  }

  // Initialize grid view toggle if it exists
  if (gridViewToggle) {
    gridViewToggle.addEventListener("change", () => {
      // If we have a current endpoint, refresh the detail view to apply the grid view
      if (AppState.getCurrentEndpoint() && window.DetailView) {
        window.DetailView.showDetailView(AppState.getCurrentEndpoint());
      }
    });
  }
}

// Update pin button icon and close button visibility based on current pin state
function updatePinButtonIcon() {
  const pinDetailBtn = document.getElementById("pin-detail");
  const closeDetailBtn = document.getElementById("close-detail");

  if (!pinDetailBtn) return;

  if (AppState.isDetailPinned()) {
    // Pinned state
    pinDetailBtn.innerHTML = "📍"; // Round pushpin for pinned state
    pinDetailBtn.title = "Unpin from right side";
    pinDetailBtn.classList.add("active");

    // Hide close button when pinned
    if (closeDetailBtn) {
      closeDetailBtn.style.display = "none";
    }
  } else {
    // Unpinned state
    pinDetailBtn.innerHTML = "📌"; // Regular pushpin for unpinned state
    pinDetailBtn.title = "Pin to right side";
    pinDetailBtn.classList.remove("active");

    // Show close button when unpinned
    if (closeDetailBtn) {
      closeDetailBtn.style.display = "inline-block";
    }
  }
}

// Toggle property sheet pin state
function togglePropertySheetPin() {
  const detailView = document.getElementById("detail-view");
  const pinDetailBtn = document.getElementById("pin-detail");
  const mainContent = document.querySelector(".main-content");
  const appContainer = document.querySelector(".app-container");

  if (!detailView || !pinDetailBtn) return;

  AppState.setDetailPinned(!AppState.isDetailPinned());

  if (AppState.isDetailPinned()) {
    // Pin the property sheet to the right side
    detailView.classList.add("pinned");

    // Add pinned-layout class to app container
    if (appContainer) {
      appContainer.classList.add("pinned-layout");
    }

    // Ensure the detail view is visible
    detailView.classList.add("active");

    // Update CSS variable for detail view width
    const detailViewWidth = getComputedStyle(detailView).width;
    document.documentElement.style.setProperty(
      "--detail-view-width",
      detailViewWidth
    );

    // Make sure the property sheet is positioned correctly
    detailView.style.top = "0";
    detailView.style.position = "relative";
  } else {
    // Unpin the property sheet
    detailView.classList.remove("pinned");

    // Remove pinned-layout class from app container
    if (appContainer) {
      appContainer.classList.remove("pinned-layout");
    }

    // Reset positioning for floating mode
    detailView.style.position = "fixed";
    detailView.style.top = "0";
    detailView.style.right = "0";
  }

  // Update pin button icon and close button visibility
  updatePinButtonIcon();

  // Update main area width after pin state change
  updateMainAreaWidth();
}

// Initialize compact view toggle
function initCompactViewToggle() {
  const toggleCompactViewBtn = document.getElementById("toggle-compact-view");

  if (!toggleCompactViewBtn) return;

  // Update button text based on initial state
  updateCompactViewButton();
}

// Toggle compact view
function toggleCompactView() {
  AppState.setCompactViewEnabled(!AppState.isCompactViewEnabled());

  // Apply compact view to grid
  const grid = document.getElementById("endpoints-grid");
  if (grid) {
    if (AppState.isCompactViewEnabled()) {
      grid.classList.add("compact");
    } else {
      grid.classList.remove("compact");
    }
  }

  // Update button text
  updateCompactViewButton();
}

// Update compact view button text
function updateCompactViewButton() {
  const toggleCompactViewBtn = document.getElementById("toggle-compact-view");

  if (!toggleCompactViewBtn) return;

  toggleCompactViewBtn.textContent = AppState.isCompactViewEnabled()
    ? "Standard View"
    : "Compact View";
  toggleCompactViewBtn.title = AppState.isCompactViewEnabled()
    ? "Switch to standard view"
    : "Switch to compact view";

  if (AppState.isCompactViewEnabled()) {
    toggleCompactViewBtn.classList.add("active");
  } else {
    toggleCompactViewBtn.classList.remove("active");
  }
}

// Initialize tooltip toggle
function initTooltipToggle() {
  const toggleTooltipsBtn = document.getElementById("toggle-tooltips-btn");
  if (!toggleTooltipsBtn) return;

  // Set initial state of the button
  updateTooltipButton(); // Call new function to set initial state
}

// Toggle tooltips
function toggleTooltips() {
  AppState.setTooltipsEnabled(!AppState.areTooltipsEnabled());
  updateTooltipButton(); // Call new function to update button
  // If tooltips are now disabled, hide any visible tooltip
  if (!AppState.areTooltipsEnabled()) {
    const tooltip = document.getElementById("endpoint-tooltip");
    if (tooltip) {
      tooltip.style.display = "none";
    }
  }
}

// Update tooltip button text and title
function updateTooltipButton() {
  const toggleTooltipsBtn = document.getElementById("toggle-tooltips-btn");
  if (toggleTooltipsBtn) {
    toggleTooltipsBtn.textContent = AppState.areTooltipsEnabled() ? "Hide Tooltips" : "Show Tooltips";
    toggleTooltipsBtn.title = AppState.areTooltipsEnabled() ? "Hide tooltips" : "Show tooltips";
  }
}

// Initialize resizable elements
function initResizableElements() {
  // Left menu resizer
  initLeftMenuResizer();

  // Detail view resizer
  initDetailViewResizer();
}

// Change font size
function changeFontSize(delta) {
  const newSize = Math.max(0.7, Math.min(1.5, AppState.getCurrentFontSize() + delta));
  AppState.setCurrentFontSize(newSize);
  applyFontSize();
}

// Reset font size
function resetFontSize() {
  AppState.setCurrentFontSize(1);
  applyFontSize();
}

// Apply font size to relevant elements
function applyFontSize() {
  const currentSize = AppState.getCurrentFontSize();
  document.documentElement.style.setProperty(
    "--font-size-multiplier",
    currentSize
  );
  document.body.style.fontSize = `${currentSize}rem`;
}

// Initialize left menu resizer
function initLeftMenuResizer() {
  const leftMenu = document.querySelector(".left-menu");
  const leftMenuResizer = document.getElementById("left-menu-resizer");
  const mainArea = document.querySelector(".main-area");

  if (!leftMenu || !leftMenuResizer) return;

  let startX, startWidth;

  leftMenuResizer.addEventListener("mousedown", (e) => {
    startX = e.clientX;
    startWidth = parseInt(getComputedStyle(leftMenu).width, 10);
    document.addEventListener("mousemove", resizeLeftMenu);
    document.addEventListener("mouseup", stopResizeLeftMenu);
    document.body.classList.add("no-select");
    e.preventDefault();
  });

  function resizeLeftMenu(e) {
    const width = startWidth + e.clientX - startX;
    const minWidth = 200;
    const maxWidth = 600;
    const constrainedWidth = Math.max(minWidth, Math.min(maxWidth, width));

    leftMenu.style.width = `${constrainedWidth}px`;
    AppState.setLastSidebarWidth(constrainedWidth);

    // Update CSS variable
    document.documentElement.style.setProperty(
      "--left-menu-width",
      `${constrainedWidth}px`
    );

    updateMainAreaWidth();
  }

  function stopResizeLeftMenu() {
    document.removeEventListener("mousemove", resizeLeftMenu);
    document.removeEventListener("mouseup", stopResizeLeftMenu);
    document.body.classList.remove("no-select");
  }
}

// Initialize detail view resizer
function initDetailViewResizer() {
  const detailView = document.getElementById("detail-view");
  const detailViewResizer = document.getElementById("detail-view-resizer");
  const mainArea = document.querySelector(".main-area");

  if (!detailView || !detailViewResizer) return;

  let startX, startWidth;

  detailViewResizer.addEventListener("mousedown", (e) => {
    startX = e.clientX;
    startWidth = parseInt(getComputedStyle(detailView).width, 10);
    document.addEventListener("mousemove", resizeDetailView);
    document.addEventListener("mouseup", stopResizeDetailView);
    document.body.classList.add("no-select");
    e.preventDefault();
  });

  function resizeDetailView(e) {
    const width = startWidth - (e.clientX - startX);
    const minWidth = 300;
    const maxWidth = 800;
    const constrainedWidth = Math.max(minWidth, Math.min(maxWidth, width));

    detailView.style.width = `${constrainedWidth}px`;

    // Update CSS variable
    document.documentElement.style.setProperty(
      "--detail-view-width",
      `${constrainedWidth}px`
    );

    updateMainAreaWidth();
  }

  function stopResizeDetailView() {
    document.removeEventListener("mousemove", resizeDetailView);
    document.removeEventListener("mouseup", stopResizeDetailView);
    document.body.classList.remove("no-select");
  }
}

// Export functions for use by other modules
window.LayoutManager = {
  updateMainAreaWidth,
  initBurgerMenu,
  toggleSidebar,
  updateBurgerIcon,
  updateBurgerTogglePosition,
  initPropertySheetPinning,
  updatePinButtonIcon,
  togglePropertySheetPin,
  initCompactViewToggle,
  toggleCompactView,
  updateCompactViewButton,
  initTooltipToggle,
  toggleTooltips,
  updateTooltipButton,
  initResizableElements,
  changeFontSize,
  resetFontSize,
  applyFontSize,
  initLeftMenuResizer,
  initDetailViewResizer
};
