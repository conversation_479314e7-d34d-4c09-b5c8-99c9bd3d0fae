// Filters and Sorting Module
// Handles all filtering and sorting functionality

// Apply all filters to the endpoint data
function applyFilters() {
  console.log("Applying filters...");
  const endpointData = AppState.getEndpointData();
  console.log(
    "endpointData length:",
    endpointData ? endpointData.length : "undefined"
  );

  if (!endpointData || endpointData.length === 0) {
    console.warn("No endpoint data to filter");
    if (window.GridManager) {
      window.GridManager.renderGrid();
    }
    return;
  }

  // Get filter values
  const searchTerm = document
    .getElementById("search-input")
    .value.toLowerCase();
  const showAdded = document.getElementById("filter-added").checked;
  const showRemoved = document.getElementById("filter-deleted").checked;
  const showModified = document.getElementById("filter-modified").checked;
  const showUnmodified = document.getElementById("filter-unmodified").checked;
  const showGet = document.getElementById("filter-get").checked;
  const showPost = document.getElementById("filter-post").checked;
  const showPut = document.getElementById("filter-put").checked;
  const showDelete = document.getElementById("filter-delete").checked;
  const showPatch = document.getElementById("filter-patch").checked;
  const showBlank = document.getElementById("filter-blank").checked;
  const showOther = document.getElementById("filter-other").checked;
  
  // Policy filter - radio buttons
  const policyAll = document.getElementById("policy-all").checked;
  const policyAllowAnonymous = document.getElementById(
    "policy-allow-anonymous"
  ).checked;
  const policyDefault = document.getElementById("policy-default").checked;
  const policyBlank = document.getElementById("policy-blank").checked;

  // API Client filter - checkboxes
  const showApiClientIncluded = document.getElementById(
    "filter-api-client-included"
  ).checked;
  const showApiClientExcluded = document.getElementById(
    "filter-api-client-excluded"
  ).checked;

  // Security Status filter - checkboxes
  const showSecurityCompliant = document.getElementById(
    "filter-security-compliant"
  ).checked;
  const showSecurityNonCompliant = document.getElementById(
    "filter-security-non-compliant"
  ).checked;
  const showSecurityRiskAccepted = document.getElementById(
    "filter-security-risk-accepted"
  ).checked;
  const showSecurityUnderReview = document.getElementById(
    "filter-security-under-review"
  ).checked;
  const showSecurityCriticalVulnerability = document.getElementById(
    "filter-security-critical-vulnerability"
  ).checked;
  const showSecurityMustReview = document.getElementById(
    "filter-security-must-review"
  ).checked;
  const showSecurityNone = document.getElementById(
    "filter-security-none"
  ).checked;

  // Security Reviews filter - checkboxes
  const showHasReviews = document.getElementById("filter-has-reviews").checked;
  const showNoReviews = document.getElementById("filter-no-reviews").checked;
  
  console.log("Filter settings:", {
    searchTerm,
    showAdded,
    showRemoved,
    showModified,
    showUnmodified,
    showGet,
    showPost,
    showPut,
    showDelete,
    showPatch,
    showBlank,
    showOther,
    policyAll,
    policyAllowAnonymous,
    policyDefault,
    policyBlank,
    showApiClientIncluded,
    showApiClientExcluded,
    showSecurityCompliant,
    showSecurityNonCompliant,
    showSecurityRiskAccepted,
    showSecurityUnderReview,
    showSecurityCriticalVulnerability,
    showSecurityMustReview,
    showSecurityNone,
    showHasReviews,
    showNoReviews,
  });
  
  // Apply filters
  let filteredData = endpointData.filter((item) => {
    // DiffType filter
    if (item.diffType === "Added" && !showAdded) return false;
    if (item.diffType === "Removed" && !showRemoved) return false;
    if (item.diffType === "Modified" && !showModified) return false;
    if (item.diffType === "Unmodified" && !showUnmodified) return false;

    // Get the endpoint object based on diffType
    const endpoint =
      item.diffType === "Removed"
        ? item.oldValue
        : item.newValue || item.oldValue;

    if (!endpoint) {
      console.warn("Endpoint is null/undefined for item:", item);
      return false;
    }

    // HTTP Methods filter - handle array of methods
    const httpMethods = endpoint.httpMethods || [];
    const hasGet = httpMethods.includes("GET");
    const hasPost = httpMethods.includes("POST");
    const hasPut = httpMethods.includes("PUT");
    const hasDelete = httpMethods.includes("DELETE");
    const hasPatch = httpMethods.includes("PATCH");
    const hasNoMethods = httpMethods.length === 0;
    const hasOtherMethods =
      httpMethods.length > 0 &&
      !hasGet &&
      !hasPost &&
      !hasPut &&
      !hasDelete &&
      !hasPatch;
      
    // Check HTTP method filtering
    let httpMethodMatch = false;

    // Handle blank/empty HTTP methods
    if (hasNoMethods) {
      httpMethodMatch = showBlank;
    } else if (hasOtherMethods) {
      httpMethodMatch = showOther;
    } else {
      // If no standard HTTP methods are selected and Blank is not selected, fail HTTP filter
      if (
        !showGet &&
        !showPost &&
        !showPut &&
        !showDelete &&
        !showPatch &&
        !showBlank &&
        !showOther
      ) {
        httpMethodMatch = false;
      } else {
        // Check if endpoint has any of the allowed methods
        httpMethodMatch =
          (hasGet && showGet) ||
          (hasPost && showPost) ||
          (hasPut && showPut) ||
          (hasDelete && showDelete) ||
          (hasPatch && showPatch);
      }
    }

    if (!httpMethodMatch) return false;
    
    // Policy filter - radio buttons (only one can be selected)
    if (!policyAll) {
      const policy = endpoint.policy || "";

      // Only one policy filter can be active at a time (radio buttons)
      if (policyAllowAnonymous) {
        return policy === "AllowAnonymous";
      }
      if (policyDefault) {
        return policy === "Default";
      }
      if (policyBlank) {
        return policy === "";
      }
    }

    // API Client filter - checkboxes (both can be selected)
    if (!showApiClientIncluded && !showApiClientExcluded) {
      // If neither is selected, show nothing
      return false;
    }

    const excludeFromApiClient = endpoint.excludeFromApiClient;
    const isIncluded = !excludeFromApiClient; // If excludeFromApiClient is false/null/undefined, it's included
    const isExcluded = excludeFromApiClient; // If excludeFromApiClient is true, it's excluded

    // Check if endpoint matches selected filter(s)
    const matchesFilter =
      (isIncluded && showApiClientIncluded) ||
      (isExcluded && showApiClientExcluded);
    if (!matchesFilter) return false;

    // Security Review filters
    // Get the latest security review for this endpoint
    const latestReview = window.getLatestSecurityReview ? window.getLatestSecurityReview(endpoint.id) : null;
    const hasReviews = latestReview !== null;

    // Security Reviews filter - Has Reviews / No Reviews
    if (!showHasReviews && !showNoReviews) {
      // If neither is selected, show nothing
      return false;
    }

    const reviewsMatch =
      (hasReviews && showHasReviews) || (!hasReviews && showNoReviews);
    if (!reviewsMatch) return false;

    // Security Status filter - only apply if endpoint has reviews
    if (hasReviews) {
      // Check if any security status filters are selected
      if (
        !showSecurityCompliant &&
        !showSecurityNonCompliant &&
        !showSecurityRiskAccepted &&
        !showSecurityUnderReview &&
        !showSecurityCriticalVulnerability &&
        !showSecurityMustReview &&
        !showSecurityNone
      ) {
        // If no security status filters are selected, show nothing
        return false;
      }

      const securityStatus = latestReview.securityStatus;
      let securityStatusMatch = false;

      switch (securityStatus) {
        case "Compliant":
          securityStatusMatch = showSecurityCompliant;
          break;
        case "Non-Compliant":
          securityStatusMatch = showSecurityNonCompliant;
          break;
        case "Risk Accepted":
          securityStatusMatch = showSecurityRiskAccepted;
          break;
        case "Under Review":
          securityStatusMatch = showSecurityUnderReview;
          break;
        case "Critical Vulnerability":
          securityStatusMatch = showSecurityCriticalVulnerability;
          break;
        case "MUST REVIEW":
          securityStatusMatch = showSecurityMustReview;
          break;
        default:
          securityStatusMatch = showSecurityNone;
          break;
      }

      if (!securityStatusMatch) return false;
    } else {
      // If endpoint has no reviews, only show if "No Review" status is selected
      if (!showSecurityNone) return false;
    }

    return true;
  });
  
  // Apply search term filter
  if (searchTerm) {
    filteredData = filteredData.filter((item) => {
      const endpoint =
        item.diffType === "Removed"
          ? item.oldValue
          : item.newValue || item.oldValue;
      if (!endpoint) return false;

      const policy = (endpoint.policy || "").toLowerCase();
      const route = (endpoint.route || "").toLowerCase();
      const actionName = (endpoint.actionName || "").toLowerCase();
      const id = (endpoint.id || "").toLowerCase();

      // Check if requestParameters includes the search term
      let requestParamsMatch = false;
      if (
        endpoint.requestParameters &&
        Array.isArray(endpoint.requestParameters)
      ) {
        requestParamsMatch = endpoint.requestParameters.some((param) => {
          const paramString = String(param).toLowerCase();
          return paramString.includes(searchTerm);
        });
      }

      // Check if security status includes the search term
      let securityStatusMatch = false;
      const latestReview = window.getLatestSecurityReview ? window.getLatestSecurityReview(endpoint.id) : null;
      if (latestReview && latestReview.securityStatus) {
        const securityStatus = latestReview.securityStatus.toLowerCase();
        securityStatusMatch = securityStatus.includes(searchTerm);
      } else {
        // For endpoints with no reviews, check if searching for "no review" or similar terms
        const noReviewTerms = [
          "no review",
          "none",
          "no security",
          "unreviewed",
        ];
        securityStatusMatch = noReviewTerms.some((term) =>
          term.includes(searchTerm)
        );
      }

      return (
        policy.includes(searchTerm) ||
        route.includes(searchTerm) ||
        actionName.includes(searchTerm) ||
        id.includes(searchTerm) ||
        requestParamsMatch ||
        securityStatusMatch
      );
    });
  }

  console.log("Filtered data length:", filteredData.length);

  // Update state
  AppState.setFilteredData(filteredData);

  // Apply sorting if needed
  const currentSort = AppState.getCurrentSort();
  if (currentSort.column && currentSort.direction) {
    sortData();
  }

  if (window.GridManager) {
    window.GridManager.renderGrid();
  }
}

// Sort the filtered data based on current sort settings
function sortData() {
  const currentSort = AppState.getCurrentSort();
  const filteredData = AppState.getFilteredData();
  
  // Debug log to verify sorting is being called
  console.log(
    `Sorting by ${currentSort.column} in ${currentSort.direction} order`
  );

  filteredData.sort((a, b) => {
    // Get the endpoint objects based on diffType
    const endpointA =
      a.diffType === "Removed" ? a.oldValue : a.newValue || a.oldValue;
    const endpointB =
      b.diffType === "Removed" ? b.oldValue : b.newValue || b.oldValue;

    let valueA, valueB;
    
    // Determine which property to sort by based on column name
    switch (currentSort.column) {
      case "Type":
        valueA = a.diffType;
        valueB = b.diffType;
        break;
      case "HTTP Methods":
        valueA = endpointA.httpMethods ? endpointA.httpMethods.join(", ") : "";
        valueB = endpointB.httpMethods ? endpointB.httpMethods.join(", ") : "";
        break;
      case "Policy":
        valueA = endpointA.policy || "";
        valueB = endpointB.policy || "";
        break;
      case "Route":
        valueA = endpointA.route;
        valueB = endpointB.route;
        break;
      case "Request Parameters":
        // For sorting, we'll still use comma-joined string even though display is line-by-line
        valueA = endpointA.requestParameters
          ? endpointA.requestParameters.join(", ")
          : "";
        valueB = endpointB.requestParameters
          ? endpointB.requestParameters.join(", ")
          : "";
        break;
      case "Security Status":
        // Get the latest security review for sorting
        const reviewA = window.getLatestSecurityReview ? window.getLatestSecurityReview(endpointA.id) : null;
        const reviewB = window.getLatestSecurityReview ? window.getLatestSecurityReview(endpointB.id) : null;
        valueA = reviewA ? reviewA.securityStatus : "No Review";
        valueB = reviewB ? reviewB.securityStatus : "No Review";
        break;
      case "Review Date":
        // Get the latest security review date for sorting
        const reviewDateA = window.getLatestSecurityReview ? window.getLatestSecurityReview(endpointA.id) : null;
        const reviewDateB = window.getLatestSecurityReview ? window.getLatestSecurityReview(endpointB.id) : null;
        // Use timestamp for proper date sorting, with no review dates sorted to the end
        valueA = reviewDateA
          ? new Date(reviewDateA.reviewDateTime).getTime()
          : 0;
        valueB = reviewDateB
          ? new Date(reviewDateB.reviewDateTime).getTime()
          : 0;
        break;
      case "Action Name":
        valueA = endpointA.actionName;
        valueB = endpointB.actionName;
        break;
      default:
        return 0;
    }

    // Handle special case for Review Date (numeric comparison)
    let comparison = 0;
    if (currentSort.column === "Review Date") {
      // Numeric comparison for timestamps
      const numValueA = Number(valueA || 0);
      const numValueB = Number(valueB || 0);

      if (numValueA > numValueB) {
        comparison = 1;
      } else if (numValueA < numValueB) {
        comparison = -1;
      }
    } else {
      // Convert to strings and make comparison case-insensitive for other columns
      const strValueA = String(valueA || "").toLowerCase();
      const strValueB = String(valueB || "").toLowerCase();

      if (strValueA > strValueB) {
        comparison = 1;
      } else if (strValueA < strValueB) {
        comparison = -1;
      }
    }

    // Reverse for descending order
    return currentSort.direction === "desc" ? comparison * -1 : comparison;
  });
  
  // Update the state with sorted data
  AppState.setFilteredData(filteredData);
}

// Initialize sortable columns
function initSortableColumns() {
  const headers = document.querySelectorAll("#endpoints-grid th");

  headers.forEach((header, index) => {
    // Check if the header already has a resizer; if so, the click is for resizing primarily.
    // Sorting should only happen if not resizing.
    const resizer = header.querySelector(".column-resizer");

    // Get column name from header text
    const columnName = header.textContent.trim();

    // Skip sorting for Review Actions column
    if (columnName === "Review Actions") {
      header.style.cursor = "default";
      return;
    }

    // Add sortable cursor and class for sortable columns
    header.style.cursor = "pointer";
    header.classList.add("sortable-header");
    header.title = `Click to sort by ${columnName}`;

    header.addEventListener("click", (e) => {
      // Check if resizing is happening
      const resizeState = AppState.getColumnResizeState();
      
      // If the click was on the resizer div, or if a resize is in progress, do not sort.
      if (
        resizeState.isAnyColumnResizing ||
        e.target === resizer ||
        window.preventNextHeaderClick
      ) {
        return;
      }

      const currentSort = AppState.getCurrentSort();
      
      // Cycle through sort states: none -> asc -> desc -> none
      if (currentSort.column === columnName) {
        if (currentSort.direction === "asc") {
          AppState.setCurrentSort(columnName, "desc");
        } else if (currentSort.direction === "desc") {
          AppState.setCurrentSort(null, null);
        }
      } else {
        AppState.setCurrentSort(columnName, "asc");
      }

      // Update header classes
      updateSortHeaders();

      // Apply sorting if needed
      const newSort = AppState.getCurrentSort();
      if (newSort.column && newSort.direction) {
        sortData();
      }

      // Re-render the grid with sorted data
      if (window.GridManager) {
        window.GridManager.renderGrid();
      }
    });
  });
}

// Update sort header classes
function updateSortHeaders() {
  const headers = document.querySelectorAll("#endpoints-grid th");
  const currentSort = AppState.getCurrentSort();

  headers.forEach((header) => {
    const columnName = header.textContent.trim();

    // Remove existing sort classes
    header.classList.remove("sort-asc", "sort-desc");

    // Add appropriate sort class if this column is being sorted
    if (currentSort.column === columnName) {
      if (currentSort.direction === "asc") {
        header.classList.add("sort-asc");
      } else if (currentSort.direction === "desc") {
        header.classList.add("sort-desc");
      }
    }
  });
}

// Toggle all checkboxes in a filter group
function toggleAllCheckboxesInGroup(groupName) {
  console.log(`Toggling all checkboxes in ${groupName} group`);

  // Find the filter group
  const filterGroups = document.querySelectorAll(".filter-group");
  let targetGroup = null;

  for (const group of filterGroups) {
    const heading = group.querySelector("h3");
    if (heading && heading.textContent.trim() === groupName) {
      targetGroup = group;
      break;
    }
  }

  if (!targetGroup) {
    console.warn(`Filter group ${groupName} not found`);
    return;
  }

  // Get all checkboxes in this group
  const checkboxes = targetGroup.querySelectorAll('input[type="checkbox"]');
  if (checkboxes.length === 0) {
    console.warn(`No checkboxes found in ${groupName} group`);
    return;
  }

  // Determine current state - if all are checked, uncheck all; otherwise, check all
  const allChecked = Array.from(checkboxes).every(
    (checkbox) => checkbox.checked
  );

  // Toggle all checkboxes
  checkboxes.forEach((checkbox) => {
    checkbox.checked = !allChecked;
  });

  // Apply filters to update the grid
  applyFilters();

  console.log(
    `${groupName} checkboxes toggled to ${
      !allChecked ? "checked" : "unchecked"
    }`
  );
}

// Export functions for use by other modules
window.FiltersSortManager = {
  applyFilters,
  sortData,
  initSortableColumns,
  updateSortHeaders,
  toggleAllCheckboxesInGroup
};
