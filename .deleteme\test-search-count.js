// Test script to verify advanced search results count functionality
// This script tests that the search results count is properly updated

console.log("🧪 Testing Advanced Search Results Count Functionality");
console.log("=====================================================");

// Function to test the results count update
function testAdvancedSearchResultsCount() {
  console.log("\n🔍 Testing Advanced Search Results Count...");
  
  // Check if the results count element exists
  const resultsCount = document.getElementById("results-count");
  console.log(`✓ Results count element found: ${!!resultsCount}`);
  
  if (resultsCount) {
    console.log(`✓ Current results count text: "${resultsCount.textContent}"`);
  } else {
    console.error("❌ Results count element with ID 'results-count' not found!");
    return false;
  }
  
  // Check if the search modal functions are available
  console.log("\n🔍 Checking function availability:");
  console.log(`✓ showAdvancedSearchModal: ${typeof showAdvancedSearchModal}`);
  console.log(`✓ executeAdvancedSearch: ${typeof executeAdvancedSearch}`);
  console.log(`✓ displaySearchResults: ${typeof displaySearchResults}`);
  console.log(`✓ getAllSecurityReviews: ${typeof getAllSecurityReviews}`);
  
  // Test the results count update directly
  if (typeof displaySearchResults === 'function') {
    console.log("\n🧪 Testing displaySearchResults with mock data...");
    
    // Mock some results
    const mockResults = [
      { id: "test1", endpointId: "endpoint1", reviewerUsername: "testuser1" },
      { id: "test2", endpointId: "endpoint2", reviewerUsername: "testuser2" },
      { id: "test3", endpointId: "endpoint3", reviewerUsername: "testuser3" }
    ];
    
    try {
      // This should update the count to 3
      displaySearchResults(mockResults);
      
      // Check if count was updated
      const updatedCount = resultsCount.textContent;
      console.log(`✓ Results count after displaying 3 results: "${updatedCount}"`);
      
      if (updatedCount === "3") {
        console.log("✅ PASS: Results count correctly updated!");
      } else {
        console.log("❌ FAIL: Results count not updated correctly");
      }
      
      // Test with empty results
      displaySearchResults([]);
      const emptyCount = resultsCount.textContent;
      console.log(`✓ Results count after displaying 0 results: "${emptyCount}"`);
      
      if (emptyCount === "0") {
        console.log("✅ PASS: Results count correctly reset to 0!");
      } else {
        console.log("❌ FAIL: Results count not reset correctly");
      }
      
    } catch (error) {
      console.error("❌ Error testing displaySearchResults:", error);
    }
  }
  
  return true;
}

// Function to manually trigger a search test
function testSearchExecution() {
  console.log("\n🔍 Testing search execution...");
  
  if (typeof executeAdvancedSearch === 'function') {
    try {
      executeAdvancedSearch();
      console.log("✅ Search executed successfully");
      
      setTimeout(() => {
        const resultsCount = document.getElementById("results-count");
        if (resultsCount) {
          console.log(`✓ Results count after search: "${resultsCount.textContent}"`);
        }
      }, 100);
      
    } catch (error) {
      console.error("❌ Error executing search:", error);
    }
  }
}

// Run the test when the page is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(testAdvancedSearchResultsCount, 2000);
  });
} else {
  setTimeout(testAdvancedSearchResultsCount, 2000);
}

// Export functions for manual testing
window.testAdvancedSearchResultsCount = testAdvancedSearchResultsCount;
window.testSearchExecution = testSearchExecution;

console.log("\n💡 Manual Testing Instructions:");
console.log("1. Open the Advanced Search modal");
console.log("2. Check that the results count shows the correct number");
console.log("3. Try different search filters and verify count updates");
console.log("4. Use 'Clear Filters' and verify count resets");
console.log("\n🔧 Manual Test Functions:");
console.log("- testAdvancedSearchResultsCount() - Test the count update");
console.log("- testSearchExecution() - Test search execution");
