// Security Review Utility Functions
// This file contains shared utility functions

// Generate unique ID for security reviews
function generateSecurityReviewId() {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substring(2, 8);
  return `sr_${timestamp}_${randomPart}`;
}

// Format date for display
function formatReviewDate(dateString) {
  if (!dateString) return "N/A";

  try {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  } catch (error) {
    console.error("Error formatting review date:", error);
    return "Invalid Date";
  }
}

// Get current ISO timestamp
function getCurrentISOTimestamp() {
  return new Date().toISOString();
}

// Get endpoint by ID from current data
function getEndpointById(endpointId) {
  if (!window.endpointData || window.endpointData.length === 0) {
    console.warn("No endpoint data available");
    return null;
  }

  const found = window.endpointData.find((item) => {
    const endpoint = item.diffType === "Removed" ? item.oldValue : item.newValue || item.oldValue;
    return endpoint && endpoint.id === endpointId;
  });

  if (!found) {
    console.warn(`Endpoint with ID ${endpointId} not found`);
    return null;
  }

  // Extract the actual endpoint data from the nested structure
  // This matches how the main application does it
  const endpoint =
    found.diffType === "Removed"
      ? found.oldValue
      : found.newValue || found.oldValue;

  // Add the diffType to the endpoint for reference
  if (endpoint) {
    endpoint.diffType = found.diffType;
  }

  return endpoint;
}

// Get current logged-in user (placeholder function - can be customized based on authentication system)
function getCurrentUser() {
  // This is a placeholder implementation. In a real application, this would
  // integrate with your authentication system to get the current user.
  // For now, we'll try to get it from various sources or use a default.

  // Try to get from localStorage (if stored by authentication system)
  const storedUser =
    localStorage.getItem("currentUser") || localStorage.getItem("username");
  if (storedUser) {
    try {
      return typeof storedUser === "string" ? storedUser : storedUser.username || "current.user";
    } catch (e) {
      console.warn("Error parsing stored user:", e);
    }
  }

  // Try to get from sessionStorage
  const sessionUser =
    sessionStorage.getItem("currentUser") || sessionStorage.getItem("username");
  if (sessionUser) {
    try {
      return typeof sessionUser === "string" ? sessionUser : sessionUser.username || "current.user";
    } catch (e) {
      console.warn("Error parsing session user:", e);
    }
  }

  // Try to get from a global variable (if set by authentication system)
  if (typeof window.currentUser !== "undefined") {
    return typeof window.currentUser === "string" ? window.currentUser : window.currentUser.username || "current.user";
  }

  // Default fallback - could be customized based on your needs
  return "current.user";
}

// Set current user (utility function for testing or manual user setting)
function setCurrentUser(username) {
  if (username && typeof username === "string" && username.trim() !== "") {
    localStorage.setItem("currentUser", username.trim());
    console.log(`Current user set to: ${username.trim()}`);
  } else {
    console.error("Invalid username provided to setCurrentUser");
  }
}

// Format current date/time for display
function formatCurrentDateTime() {
  const now = new Date();
  return now.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: false,
  });
}

// Get security status display class
function getSecurityStatusClass(status) {
  if (!status) return "security-status-unknown";

  switch (status) {
    case SECURITY_STATUS.COMPLIANT:
      return "security-status-compliant";
    case SECURITY_STATUS.NON_COMPLIANT:
      return "security-status-non-compliant";
    case SECURITY_STATUS.RISK_ACCEPTED:
      return "security-status-risk-accepted";
    case SECURITY_STATUS.UNDER_REVIEW:
      return "security-status-under-review";
    case SECURITY_STATUS.CRITICAL_VULNERABILITY:
      return "security-status-critical";
    case SECURITY_STATUS.MUST_REVIEW:
      return "security-status-must-review";
    default:
      return "security-status-unknown";
  }
}

// Export for browser environment
if (typeof window !== "undefined") {
  window.generateSecurityReviewId = generateSecurityReviewId;
  window.formatReviewDate = formatReviewDate;
  window.getCurrentISOTimestamp = getCurrentISOTimestamp;
  window.getEndpointById = getEndpointById;
  window.getCurrentUser = getCurrentUser;
  window.setCurrentUser = setCurrentUser;
  window.formatCurrentDateTime = formatCurrentDateTime;
  window.getSecurityStatusClass = getSecurityStatusClass;
}

// Export for module environment
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    generateSecurityReviewId,
    formatReviewDate,
    getCurrentISOTimestamp,
    getEndpointById,
    getCurrentUser,
    setCurrentUser,
    formatCurrentDateTime,
    getSecurityStatusClass
  };
}
