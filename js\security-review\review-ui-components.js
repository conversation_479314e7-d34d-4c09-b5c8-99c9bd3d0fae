// Security Review UI Components
// This file contains functions for creating UI elements

// Create security status cell content
function createSecurityStatusCell(endpoint) {
  const latestReview = getLatestSecurityReview(endpoint.id);

  if (!latestReview) {
    return '<span class="security-status security-status-unknown">Not Reviewed</span>';
  }

  const statusClass = getSecurityStatusClass(latestReview.securityStatus);
  return `<span class="security-status ${statusClass}">${latestReview.securityStatus}</span>`;
}

// Create review date cell content
function createReviewDateCell(endpoint) {
  const latestReview = getLatestSecurityReview(endpoint.id);

  if (!latestReview) {
    return '<span class="text-muted">No reviews</span>';
  }

  return formatReviewDate(latestReview.reviewDateTime);
}

// Create security actions cell content
function createSecurityActionsCell(endpoint) {
  const hasReviews = getSecurityReviewsForEndpoint(endpoint.id).length > 0;

  // Extract endpoint information - handle the nested structure properly
  const route =
    endpoint.route ||
    endpoint.newValue?.route ||
    endpoint.oldValue?.route ||
    "Unknown Route";

  // Handle HTTP methods properly - check multiple possible sources
  let httpMethod = "Unknown";
  if (
    endpoint.httpMethods &&
    Array.isArray(endpoint.httpMethods) &&
    endpoint.httpMethods.length > 0
  ) {
    httpMethod = endpoint.httpMethods.join(", ");
  } else if (endpoint.newValue?.httpMethods && Array.isArray(endpoint.newValue.httpMethods)) {
    httpMethod = endpoint.newValue.httpMethods.join(", ");
  } else if (endpoint.oldValue?.httpMethods && Array.isArray(endpoint.oldValue.httpMethods)) {
    httpMethod = endpoint.oldValue.httpMethods.join(", ");
  } else if (endpoint.httpMethod) {
    httpMethod = endpoint.httpMethod;
  } else if (endpoint.newValue?.httpMethod) {
    httpMethod = endpoint.newValue.httpMethod;
  } else if (endpoint.oldValue?.httpMethod) {
    httpMethod = endpoint.oldValue.httpMethod;
  }

  return `
    <div class="security-actions">
      <button class="security-action-btn add-review-btn" 
              data-endpoint-id="${endpoint.id}" 
              data-http-method="${httpMethod}" 
              data-route="${route}" 
              title="Add Security Review">
        <i class="bi bi-shield-plus"></i> Add
      </button>
      ${
        hasReviews
          ? `<button class="security-action-btn view-history-btn" 
                    data-endpoint-id="${endpoint.id}" 
                    data-http-method="${httpMethod}" 
                    data-route="${route}" 
                    title="View Review History">
          <i class="bi bi-clock-history"></i> History
        </button>`
          : ""
      }
    </div>
  `;
}

// Create a table row for a security review (used in history and search)
function createReviewTableRow(review, options = {}) {
  const row = document.createElement("tr");
  
  // Format the date
  const formattedDate = formatReviewDate(review.reviewDateTime);
  const timestamp = new Date(review.reviewDateTime).getTime();
  
  // Create the date cell
  const dateCell = document.createElement("td");
  dateCell.setAttribute("data-sort-value", timestamp);
  dateCell.textContent = formattedDate;
  
  // Create cells based on options
  if (options.includeEndpoint) {
    // Get endpoint information for search results
    const endpoint = getEndpointById(review.endpointId);
    const endpointInfo = extractEndpointInfo ? extractEndpointInfo(endpoint) : { route: "Unknown", methodString: "Unknown" };
    
    const endpointCell = document.createElement("td");
    endpointCell.textContent = endpointInfo.route;
    endpointCell.title = endpointInfo.route;
    
    const methodCell = document.createElement("td");
    methodCell.innerHTML = `<span class="badge bg-secondary">${endpointInfo.methodString}</span>`;
    
    row.appendChild(dateCell);
    row.appendChild(endpointCell);
    row.appendChild(methodCell);
  } else {
    // Just add date cell for history view
    row.appendChild(dateCell);
  }
  
  // Create the reviewer cell
  const reviewerCell = document.createElement("td");
  reviewerCell.textContent = review.reviewerUsername;
  
  // Create the status cell with appropriate styling
  const statusCell = document.createElement("td");
  const statusClass = getSecurityStatusClass(review.securityStatus);
  statusCell.innerHTML = `<span class="security-status ${statusClass}">${review.securityStatus}</span>`;
  
  // Create the notes cell with expand/collapse functionality
  const notesCell = document.createElement("td");
  
  // Check if notes are empty
  if (!review.reviewNotes || review.reviewNotes.trim() === "") {
    notesCell.innerHTML = '<em class="text-muted">No notes provided</em>';
  } else {
    // Create a container for the notes
    const notesContainer = document.createElement("div");
    notesContainer.className = "review-notes-container";
    
    // Create a preview of the notes (first 100 characters)
    const notesPreview = document.createElement("div");
    notesPreview.className = "review-notes-preview";
    const previewText = review.reviewNotes.length > 100 
      ? review.reviewNotes.substring(0, 100) + "..." 
      : review.reviewNotes;
    notesPreview.textContent = previewText;
    
    // Create the full notes content (initially hidden)
    const notesContent = document.createElement("div");
    notesContent.className = "review-notes-content d-none";
    
    // Convert markdown to HTML if a markdown parser is available
    if (typeof marked === 'function') {
      try {
        notesContent.innerHTML = marked.parse(review.reviewNotes);
      } catch (error) {
        console.warn("Error parsing markdown:", error);
        notesContent.textContent = review.reviewNotes;
      }
    } else {
      notesContent.textContent = review.reviewNotes;
    }
    
    // Create expand/collapse toggle button if notes are long enough
    if (review.reviewNotes.length > 100) {
      const toggleButton = document.createElement("button");
      toggleButton.className = "btn btn-sm btn-link review-notes-toggle";
      toggleButton.textContent = "Show more";
      toggleButton.setAttribute("data-expanded", "false");
      toggleButton.setAttribute("data-review-id", review.id);
      
      notesContainer.appendChild(toggleButton);
    }
    
    // Add elements to the notes cell
    notesContainer.appendChild(notesPreview);
    notesContainer.appendChild(notesContent);
    notesCell.appendChild(notesContainer);
  }
  
  // Add cells to the row
  row.appendChild(reviewerCell);
  row.appendChild(statusCell);
  row.appendChild(notesCell);
  
  // Add click handler to row for viewing endpoint details (if in search mode)
  if (options.includeEndpoint && options.enableRowClick) {
    row.style.cursor = "pointer";
    row.addEventListener("click", function(e) {
      // Don't trigger if clicking on the notes toggle button
      if (e.target.closest(".review-notes-toggle")) {
        return;
      }
      
      const endpoint = getEndpointById(review.endpointId);
      if (endpoint && typeof window.showDetailView === 'function') {
        // Close the search modal first
        const searchModal = document.getElementById("advancedSecuritySearchModal");
        if (searchModal) {
          const modalInstance = bootstrap.Modal.getInstance(searchModal);
          if (modalInstance) {
            modalInstance.hide();
          }
        }
        
        // Show the endpoint detail view
        window.showDetailView(endpoint);
      }
    });
  }
  
  return row;
}

// Initialize expand/collapse functionality for review notes
function initReviewNotesToggle() {
  // Use event delegation for toggle buttons
  document.addEventListener("click", function(e) {
    const toggleButton = e.target.closest(".review-notes-toggle");
    if (!toggleButton) return;
    
    // Get the notes container
    const notesContainer = toggleButton.closest(".review-notes-container");
    if (!notesContainer) return;
    
    // Get the preview and content elements
    const preview = notesContainer.querySelector(".review-notes-preview");
    const content = notesContainer.querySelector(".review-notes-content");
    
    // Check if currently expanded
    const isExpanded = toggleButton.getAttribute("data-expanded") === "true";
    
    if (isExpanded) {
      // Collapse
      preview.classList.remove("d-none");
      content.classList.add("d-none");
      toggleButton.textContent = "Show more";
      toggleButton.setAttribute("data-expanded", "false");
    } else {
      // Expand
      preview.classList.add("d-none");
      content.classList.remove("d-none");
      toggleButton.textContent = "Show less";
      toggleButton.setAttribute("data-expanded", "true");
    }
    
    // Prevent event bubbling
    e.stopPropagation();
  });
}

// Initialize UI components when DOM is ready
function initReviewUIComponents() {
  console.log("Initializing security review UI components");
  
  // Initialize notes toggle functionality
  initReviewNotesToggle();
  
  console.log("Security review UI components initialized");
}

// Export for browser environment
if (typeof window !== "undefined") {
  window.createSecurityStatusCell = createSecurityStatusCell;
  window.createReviewDateCell = createReviewDateCell;
  window.createSecurityActionsCell = createSecurityActionsCell;
  window.createReviewTableRow = createReviewTableRow;
  window.initReviewNotesToggle = initReviewNotesToggle;
  window.initReviewUIComponents = initReviewUIComponents;
}

// Export for module environment
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    createSecurityStatusCell,
    createReviewDateCell,
    createSecurityActionsCell,
    createReviewTableRow,
    initReviewNotesToggle,
    initReviewUIComponents
  };
}
