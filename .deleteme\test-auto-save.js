// Test Auto-Save Functionality for Security Reviews
// This script tests the automatic saving of security reviews

console.log("🧪 Testing auto-save functionality for security reviews");

// Test function to simulate security review creation and auto-save
function testAutoSave() {
  console.log("🚀 Starting auto-save test...");
  
  // Check if required functions are available
  console.log("🔍 Function availability check:");
  console.log(`  - AppState: ${typeof AppState}`);
  console.log(`  - AppState.getLastLoadedFileName: ${typeof AppState?.getLastLoadedFileName}`);
  console.log(`  - saveSecurityReviewsToFile: ${typeof saveSecurityReviewsToFile}`);
  console.log(`  - DataLoader: ${typeof DataLoader}`);
  console.log(`  - DataLoader.saveSecurityReviewsToFile: ${typeof DataLoader?.saveSecurityReviewsToFile}`);
  console.log(`  - createSecurityReview: ${typeof createSecurityReview}`);
  console.log(`  - getAllSecurityReviews: ${typeof getAllSecurityReviews}`);
  
  // Test 1: Check if we can get the filename
  try {
    const fileName = AppState?.getLastLoadedFileName() || "test-endpoint-data.json";
    console.log(`✅ Test 1 - Filename retrieval: ${fileName}`);
  } catch (error) {
    console.error(`❌ Test 1 - Filename retrieval failed: ${error.message}`);
  }
  
  // Test 2: Check if we can create a test security review
  try {
    const testReviewData = {
      endpointId: "test-endpoint-id",
      reviewDateTime: new Date().toISOString(),
      reviewerUsername: "test-user",
      securityStatus: "Compliant",
      reviewNotes: "Test review for auto-save functionality"
    };
    
    if (typeof createSecurityReview === 'function') {
      const reviewsBefore = typeof getAllSecurityReviews === 'function' ? getAllSecurityReviews().length : 0;
      console.log(`📊 Reviews before test creation: ${reviewsBefore}`);
      
      // Create a test review (but we'll clean it up after)
      const testReview = createSecurityReview(testReviewData);
      
      const reviewsAfter = typeof getAllSecurityReviews === 'function' ? getAllSecurityReviews().length : 0;
      console.log(`📊 Reviews after test creation: ${reviewsAfter}`);
      
      if (reviewsAfter > reviewsBefore) {
        console.log("✅ Test 2 - Security review creation: PASSED");
        
        // Test 3: Test the save functionality
        const fileName = "test-auto-save.json";
        if (typeof saveSecurityReviewsToFile === 'function') {
          saveSecurityReviewsToFile(fileName);
          console.log("✅ Test 3 - Save function call: PASSED (download should be triggered)");
        } else if (typeof DataLoader !== 'undefined' && DataLoader.saveSecurityReviewsToFile) {
          DataLoader.saveSecurityReviewsToFile(fileName);
          console.log("✅ Test 3 - Save function call via DataLoader: PASSED (download should be triggered)");
        } else {
          console.error("❌ Test 3 - Save function call: FAILED (no save function available)");
        }
        
        // Clean up: remove the test review
        if (typeof deleteSecurityReview === 'function') {
          deleteSecurityReview(testReview.id);
          console.log("🧹 Cleanup: Test review removed");
        }
      } else {
        console.error("❌ Test 2 - Security review creation: FAILED (review not added)");
      }
    } else {
      console.error("❌ Test 2 - Security review creation: FAILED (createSecurityReview not available)");
    }
  } catch (error) {
    console.error(`❌ Test 2 - Security review creation failed: ${error.message}`);
  }
  
  console.log("🏁 Auto-save test completed");
}

// Run test when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', testAutoSave);
} else {
  // DOM is already ready
  setTimeout(testAutoSave, 1000); // Wait a bit for other scripts to initialize
}

// Export for manual testing
window.testAutoSave = testAutoSave;
