#!/usr/bin/env python3
"""
Simple HTTP Server for ApiEndpointExplorer_v3
This script starts a local HTTP server to serve the application files,
which is needed to avoid CORS issues when loading component files.
"""

import http.server
import socketserver
import os
import sys

# Set the port number
PORT = 8000

# Change to the directory containing this script
os.chdir(os.path.dirname(os.path.abspath(__file__)))

# Create a simple HTTP request handler
class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers to allow local file access
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

# Start the server
if __name__ == "__main__":
    handler = MyHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", PORT), handler) as httpd:
            print(f"Server running at http://localhost:{PORT}")
            print(f"Open http://localhost:{PORT}/index.html in your browser")
            print("Press Ctrl+C to stop the server")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nServer stopped.")
        sys.exit(0)
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"Port {PORT} is already in use. Try a different port:")
            print(f"python3 server.py {PORT + 1}")
        else:
            print(f"Error starting server: {e}")
        sys.exit(1)
