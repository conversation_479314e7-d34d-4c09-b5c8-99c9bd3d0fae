// Column Resizing Module
// Handles all table column resizing functionality

// Initialize table column resizing functionality
function initTableColumnResizing() {
  const grid = document.getElementById("endpoints-grid");
  const resizeState = AppState.getColumnResizeState();
  
  if (!grid || resizeState.columnResizerInitialized) return;

  const headers = Array.from(grid.querySelectorAll("thead th"));
  if (headers.length === 0) return;

  // Get CSS-defined constraints for each column
  headers.forEach((header, index) => {
    const style = getComputedStyle(header);
    const minWidth =
      parseInt(style.minWidth) ||
      resizeState.defaultMinColumnWidths[index] ||
      resizeState.MIN_COLUMN_WIDTH;
    const maxWidth =
      parseInt(style.maxWidth) ||
      resizeState.defaultMaxColumnWidths[index] ||
      resizeState.MAX_COLUMN_WIDTH;

    // Update our constraint arrays with actual CSS values
    resizeState.defaultMinColumnWidths[index] = minWidth;
    resizeState.defaultMaxColumnWidths[index] = maxWidth;
  });

  if (resizeState.defaultColumnWidths.length === 0) {
    calculateAndApplyColumnWidths(grid, headers, resizeState.defaultColumnWidths);
    console.log(
      "Default column widths calculated (proportional, accounting for padding/border):",
      resizeState.defaultColumnWidths
    );
  } else {
    // Apply stored defaults if they exist.
    headers.forEach((header, index) => {
      if (resizeState.defaultColumnWidths[index] !== undefined) {
        header.style.width = `${resizeState.defaultColumnWidths[index]}px`;
      }
    });
  }

  headers.forEach((header, index) => {
    const resizer = header.querySelector(".column-resizer");
    if (resizer) {
      resizer.addEventListener("mousedown", (e) =>
        handleColumnMouseDown(e, header, index, headers)
      );
    }
  });
  
  // Update state
  AppState.setColumnResizeState({ columnResizerInitialized: true });
}

function calculateAndApplyColumnWidths(
  grid,
  headers,
  targetDefaultWidthsArray
) {
  const availableGridWidth = grid.clientWidth;
  let totalPaddingAndBorder = 0;
  const initialDesiredContentWidths = [];
  const resizeState = AppState.getColumnResizeState();

  headers.forEach((header, index) => {
    const style = getComputedStyle(header);
    // For recalculation (reset), header might have an inline style.
    // We want the 'natural' or CSS-defined width as the basis for desired width.
    const originalInlineWidth = header.style.width;
    header.style.width = ""; // Temporarily remove to get underlying width
    initialDesiredContentWidths.push(
      parseFloat(getComputedStyle(header).width) || 0
    );
    header.style.width = originalInlineWidth; // Restore it

    totalPaddingAndBorder +=
      (parseFloat(style.paddingLeft) || 0) +
      (parseFloat(style.paddingRight) || 0) +
      (parseFloat(style.borderLeftWidth) || 0) +
      (parseFloat(style.borderRightWidth) || 0);
  });

  const contentWidthForDistribution = Math.max(
    0,
    availableGridWidth - totalPaddingAndBorder
  );

  // Calculate sum of minimum column widths based on CSS-defined values
  let sumOfMinContentWidths = 0;
  headers.forEach((header, index) => {
    sumOfMinContentWidths += resizeState.defaultMinColumnWidths[index] || resizeState.MIN_COLUMN_WIDTH;
  });

  if (
    contentWidthForDistribution < sumOfMinContentWidths &&
    headers.length > 0
  ) {
    console.warn(
      "Calculated content width for distribution is less than sum of min widths. Setting all to minimum widths."
    );
    headers.forEach((header, index) => {
      const minWidth = resizeState.defaultMinColumnWidths[index] || resizeState.MIN_COLUMN_WIDTH;
      targetDefaultWidthsArray[index] = minWidth;
      header.style.width = `${minWidth}px`;
    });
  } else if (headers.length > 0) {
    // Proceed if there are headers
    const spaceAboveMins = contentWidthForDistribution - sumOfMinContentWidths;
    let sumOfInitialDesiredWidthsAdjustedForMin = 0;
    const desiredWidthsAdjustedForMin = initialDesiredContentWidths.map(
      (w, i) => {
        const minWidth = resizeState.defaultMinColumnWidths[i] || resizeState.MIN_COLUMN_WIDTH;
        const wantedAboveMin = Math.max(0, w - minWidth);
        sumOfInitialDesiredWidthsAdjustedForMin += wantedAboveMin;
        return wantedAboveMin;
      }
    );
    headers.forEach((header, index) => {
      const minWidth = resizeState.defaultMinColumnWidths[index] || resizeState.MIN_COLUMN_WIDTH;
      const maxWidth = resizeState.defaultMaxColumnWidths[index] || resizeState.MAX_COLUMN_WIDTH;

      let finalContentWidth = minWidth;
      if (sumOfInitialDesiredWidthsAdjustedForMin > 0) {
        finalContentWidth +=
          (desiredWidthsAdjustedForMin[index] /
            sumOfInitialDesiredWidthsAdjustedForMin) *
          spaceAboveMins;
      } else {
        // If no specific desires above min, or all initial are 0, distribute equally
        finalContentWidth += spaceAboveMins / headers.length;
      }

      // Ensure width is within min-max constraints
      finalContentWidth = Math.max(finalContentWidth, minWidth);
      finalContentWidth = Math.min(finalContentWidth, maxWidth);

      targetDefaultWidthsArray[index] = finalContentWidth;
      header.style.width = `${finalContentWidth}px`;
    });
  }
}

function handleColumnMouseDown(e, header, headerIndex, allHeaders) {
  e.preventDefault();
  e.stopPropagation(); // Prevent sorting or other header actions

  const resizeState = AppState.getColumnResizeState();

  // Check for double-click to auto-fit column
  const now = new Date().getTime();
  const doubleClickThreshold = 300; // milliseconds

  if (
    resizeState.lastColumnResizeTarget === header &&
    now - resizeState.lastColumnResizeClick < doubleClickThreshold
  ) {
    // This is a double-click, trigger auto-fit for this column
    e.stopPropagation(); // Prevent the click from bubbling up to the header

    // Set a flag to prevent sorting in case the event still bubbles
    window.preventNextHeaderClick = true;
    setTimeout(() => {
      window.preventNextHeaderClick = false;
    }, 100);

    autoFitColumnToContent(header, headerIndex);

    // Reset tracking variables
    AppState.setColumnResizeState({
      lastColumnResizeClick: 0,
      lastColumnResizeTarget: null
    });
    return;
  }

  // Update tracking variables for potential future double-click
  AppState.setColumnResizeState({
    lastColumnResizeClick: now,
    lastColumnResizeTarget: header
  });

  // Continue with normal resizing logic for single click
  AppState.setColumnResizeState({
    isAnyColumnResizing: true,
    columnResizeTargetHeader: header,
    columnResizeStartX: e.clientX,
    columnResizeStartWidth: parseFloat(getComputedStyle(header).width)
  });

  let nextHeader = null;
  let nextHeaderStartWidth = 0;

  // The resizer on header `headerIndex` affects `headerIndex` and `headerIndex + 1`.
  if (headerIndex < allHeaders.length - 1) {
    nextHeader = allHeaders[headerIndex + 1];
    nextHeaderStartWidth = parseFloat(getComputedStyle(nextHeader).width);
  }

  AppState.setColumnResizeState({
    columnResizeNextHeader: nextHeader,
    columnResizeNextHeaderStartWidth: nextHeaderStartWidth
  });

  const grid = document.getElementById("endpoints-grid");

  // Add active class to resizer for visual feedback
  const resizer = header.querySelector(".column-resizer");
  if (resizer) resizer.classList.add("active");

  document.body.classList.add("no-select"); // Prevent text selection during resize
  if (grid) grid.style.cursor = "col-resize";

  // Set ALL column widths to their current pixel widths to prevent browser auto-adjustments
  allHeaders.forEach((th) => {
    const currentThWidth = parseFloat(getComputedStyle(th).width);
    th.style.width = `${currentThWidth}px`;
    th.style.minWidth = `${currentThWidth}px`;
  });

  document.addEventListener("mousemove", handleColumnMouseMove);
  document.addEventListener("mouseup", handleColumnMouseUp);

  console.log(
    `Mousedown on: ${header.textContent.trim()}, StartX: ${e.clientX}, StartWidth: ${parseFloat(getComputedStyle(header).width)}`
  );
  if (nextHeader) {
    console.log(
      `Next header: ${nextHeader.textContent.trim()}, NextHeaderStartWidth: ${nextHeaderStartWidth}`
    );
  }
}

function handleColumnMouseMove(e) {
  const resizeState = AppState.getColumnResizeState();
  
  if (!resizeState.isAnyColumnResizing || !resizeState.columnResizeTargetHeader) return;
  e.preventDefault();

  const deltaX = e.clientX - resizeState.columnResizeStartX;
  let newTargetWidth = resizeState.columnResizeStartWidth + deltaX;

  // Get the current header index for applying column-specific constraints
  const headerIndex = Array.from(
    document.querySelectorAll("#endpoints-grid thead th")
  ).indexOf(resizeState.columnResizeTargetHeader);

  // Get column-specific min/max constraints
  const minTargetWidth =
    resizeState.defaultMinColumnWidths[headerIndex] || resizeState.MIN_COLUMN_WIDTH;
  const maxTargetWidth =
    resizeState.defaultMaxColumnWidths[headerIndex] || resizeState.MAX_COLUMN_WIDTH;

  if (resizeState.columnResizeNextHeader) {
    const nextHeaderIndex = headerIndex + 1;
    const minNextWidth =
      resizeState.defaultMinColumnWidths[nextHeaderIndex] || resizeState.MIN_COLUMN_WIDTH;
    const maxNextWidth =
      resizeState.defaultMaxColumnWidths[nextHeaderIndex] || resizeState.MAX_COLUMN_WIDTH;

    const combinedInitialWidth =
      resizeState.columnResizeStartWidth + resizeState.columnResizeNextHeaderStartWidth;
    let newNextHeaderWidth = resizeState.columnResizeNextHeaderStartWidth - deltaX;

    // Apply constraints to targetHeader
    if (newTargetWidth < minTargetWidth) {
      newTargetWidth = minTargetWidth;
      newNextHeaderWidth = combinedInitialWidth - newTargetWidth;
    } else if (newTargetWidth > maxTargetWidth) {
      newTargetWidth = maxTargetWidth;
      newNextHeaderWidth = combinedInitialWidth - newTargetWidth;
    }

    // Apply constraints to nextHeader
    if (newNextHeaderWidth < minNextWidth) {
      newNextHeaderWidth = minNextWidth;
      newTargetWidth = combinedInitialWidth - newNextHeaderWidth;
      if (newTargetWidth < minTargetWidth) newTargetWidth = minTargetWidth;
      if (newTargetWidth > maxTargetWidth) newTargetWidth = maxTargetWidth;
    } else if (newNextHeaderWidth > maxNextWidth) {
      newNextHeaderWidth = maxNextWidth;
      newTargetWidth = combinedInitialWidth - newNextHeaderWidth;
      if (newTargetWidth < minTargetWidth) newTargetWidth = minTargetWidth;
      if (newTargetWidth > maxTargetWidth) newTargetWidth = maxTargetWidth;
    }

    resizeState.columnResizeTargetHeader.style.width = `${newTargetWidth}px`;
    resizeState.columnResizeTargetHeader.style.minWidth = `${newTargetWidth}px`;
    if (newTargetWidth < 1) {
      resizeState.columnResizeTargetHeader.style.paddingLeft = "0px";
      resizeState.columnResizeTargetHeader.style.paddingRight = "0px";
    } else {
      resizeState.columnResizeTargetHeader.style.paddingLeft = "";
      resizeState.columnResizeTargetHeader.style.paddingRight = "";
    }

    resizeState.columnResizeNextHeader.style.width = `${newNextHeaderWidth}px`;
    resizeState.columnResizeNextHeader.style.minWidth = `${newNextHeaderWidth}px`;
    if (newNextHeaderWidth < 1) {
      resizeState.columnResizeNextHeader.style.paddingLeft = "0px";
      resizeState.columnResizeNextHeader.style.paddingRight = "0px";
    } else {
      resizeState.columnResizeNextHeader.style.paddingLeft = "";
      resizeState.columnResizeNextHeader.style.paddingRight = "";
    }
  } else {
    // Single column resize (rightmost column)
    if (newTargetWidth < minTargetWidth) {
      newTargetWidth = minTargetWidth;
    } else if (newTargetWidth > maxTargetWidth) {
      newTargetWidth = maxTargetWidth;
    }
    
    resizeState.columnResizeTargetHeader.style.width = `${newTargetWidth}px`;
    resizeState.columnResizeTargetHeader.style.minWidth = `${newTargetWidth}px`;
    if (newTargetWidth < 1) {
      resizeState.columnResizeTargetHeader.style.paddingLeft = "0px";
      resizeState.columnResizeTargetHeader.style.paddingRight = "0px";
    } else {
      resizeState.columnResizeTargetHeader.style.paddingLeft = "";
      resizeState.columnResizeTargetHeader.style.paddingRight = "";
    }
  }

  updateAllCellWidths();
}

function handleColumnMouseUp(e) {
  const resizeState = AppState.getColumnResizeState();
  
  if (!resizeState.isAnyColumnResizing) return;
  
  setTimeout(() => {
    AppState.setColumnResizeState({ isAnyColumnResizing: false });
  }, 0);

  const resizer = resizeState.columnResizeTargetHeader.querySelector(".column-resizer");
  if (resizer) resizer.classList.remove("active");

  document.body.classList.remove("no-select");
  const grid = document.getElementById("endpoints-grid");
  if (grid) grid.style.cursor = "default";

  // Remove event listeners
  document.removeEventListener("mousemove", handleColumnMouseMove);
  document.removeEventListener("mouseup", handleColumnMouseUp);

  console.log(
    `Mouseup. Final width for ${resizeState.columnResizeTargetHeader.textContent.trim()}: ${
      resizeState.columnResizeTargetHeader.style.width
    }`
  );
  if (resizeState.columnResizeNextHeader) {
    console.log(
      `Final width for ${resizeState.columnResizeNextHeader.textContent.trim()}: ${
        resizeState.columnResizeNextHeader.style.width
      }`
    );
  }

  AppState.setColumnResizeState({
    columnResizeTargetHeader: null,
    columnResizeNextHeader: null
  });
}

// Auto-fit column width to content
function autoFitColumnToContent(header, headerIndex) {
  console.log(`Auto-fitting column: ${header.textContent.trim()}`);

  const grid = document.getElementById("endpoints-grid");
  if (!grid) return;

  // Get all cells in this column
  const rows = Array.from(grid.querySelectorAll("tbody tr"));
  const columnCells = rows
    .map((row) => row.cells[headerIndex])
    .filter((cell) => cell);

  if (columnCells.length === 0) return;

  // Calculate the max width needed for content
  let maxContentWidth = 0;

  // Create a temporary div to measure text width
  const measuringDiv = document.createElement("div");
  measuringDiv.style.position = "absolute";
  measuringDiv.style.visibility = "hidden";
  measuringDiv.style.whiteSpace = "nowrap";
  measuringDiv.style.left = "-9999px";
  measuringDiv.style.top = "0";

  // Copy the current cell styling to ensure accurate measurement
  const cellStyle = window.getComputedStyle(columnCells[0]);
  measuringDiv.style.fontSize = cellStyle.fontSize;
  measuringDiv.style.fontFamily = cellStyle.fontFamily;
  measuringDiv.style.fontWeight = cellStyle.fontWeight;
  measuringDiv.style.letterSpacing = cellStyle.letterSpacing;
  measuringDiv.style.padding = "0";

  document.body.appendChild(measuringDiv);

  // Measure header content first
  measuringDiv.textContent = header.textContent.trim();
  let headerTextWidth = measuringDiv.getBoundingClientRect().width;

  // Add padding to header width
  const headerPadding =
    parseFloat(window.getComputedStyle(header).paddingLeft) +
    parseFloat(window.getComputedStyle(header).paddingRight);

  // Add extra space for sort icons if present
  let sortIconWidth = 0;
  if (
    header.classList.contains("sort-asc") ||
    header.classList.contains("sort-desc")
  ) {
    sortIconWidth = 20;
  }

  // Account for column resizer width
  const resizer = header.querySelector(".column-resizer");
  let resizerWidth = 0;
  if (resizer) {
    resizerWidth = parseFloat(window.getComputedStyle(resizer).width) || 8;
  }

  // Initial max is the header width
  maxContentWidth =
    headerTextWidth + headerPadding + sortIconWidth + resizerWidth;
    
  // Now measure all cell contents
  columnCells.forEach((cell) => {
    // For cells with HTML content (like cells with <br> tags)
    if (cell.innerHTML.includes("<br>")) {
      // Split by <br> and measure each line
      const content = cell.innerHTML.split(/<br\s*\/?>/i);
      content.forEach((line) => {
        // Strip any HTML tags for measurement
        const plainText = line.replace(/<[^>]*>/g, "");
        measuringDiv.textContent = plainText;
        const lineWidth = measuringDiv.getBoundingClientRect().width;
        maxContentWidth = Math.max(maxContentWidth, lineWidth);
      });
    } else {
      // Regular text content
      measuringDiv.textContent = cell.textContent;
      const cellContentWidth = measuringDiv.getBoundingClientRect().width;

      // Add cell padding
      const cellPadding =
        parseFloat(window.getComputedStyle(cell).paddingLeft) +
        parseFloat(window.getComputedStyle(cell).paddingRight);

      maxContentWidth = Math.max(
        maxContentWidth,
        cellContentWidth + cellPadding
      );
    }
  });

  // Cleanup
  document.body.removeChild(measuringDiv);

  // Add some buffer space for better appearance
  const bufferSpace = 20;
  maxContentWidth += bufferSpace;

  // Apply min/max constraints
  const resizeState = AppState.getColumnResizeState();
  const minWidth = resizeState.defaultMinColumnWidths[headerIndex] || resizeState.MIN_COLUMN_WIDTH;
  const maxWidth = resizeState.defaultMaxColumnWidths[headerIndex] || resizeState.MAX_COLUMN_WIDTH;

  maxContentWidth = Math.max(maxContentWidth, minWidth);
  maxContentWidth = Math.min(maxContentWidth, maxWidth);

  console.log(
    `Calculated width for ${header.textContent.trim()}: ${maxContentWidth}px`
  );

  // Apply the new width to the header
  header.style.width = `${maxContentWidth}px`;
  header.style.minWidth = `${maxContentWidth}px`;

  // Update all cells in this column
  columnCells.forEach((cell) => {
    cell.style.width = `${maxContentWidth}px`;
    cell.style.maxWidth = `${maxContentWidth}px`;
  });

  // Adjust other columns if needed (to maintain table width)
  if (headerIndex < grid.querySelectorAll("thead th").length - 1) {
    updateAllCellWidths();
  }
}

function updateAllCellWidths() {
  const grid = document.getElementById("endpoints-grid");
  if (!grid) return;

  const headers = Array.from(grid.querySelectorAll("thead th"));
  const rows = Array.from(grid.querySelectorAll("tbody tr"));

  headers.forEach((header, index) => {
    const headerWidth = parseFloat(header.style.width);

    rows.forEach((row) => {
      const cell = row.cells[index];
      if (cell) {
        cell.style.width = `${headerWidth}px`;
        cell.style.maxWidth = `${headerWidth}px`;

        // If column width is effectively zero, remove padding to make it visually disappear
        if (headerWidth < 1) {
          cell.style.paddingLeft = "0px";
          cell.style.paddingRight = "0px";
        } else {
          // Restore default padding by removing inline styles
          cell.style.paddingLeft = "";
          cell.style.paddingRight = "";
        }
      }
    });
  });
}

function resetColumnWidthsToDefaults() {
  const grid = document.getElementById("endpoints-grid");
  if (!grid) return;

  const headers = Array.from(grid.querySelectorAll("thead th"));
  if (headers.length === 0) return;

  const resizeState = AppState.getColumnResizeState();

  // First, clear inline styles to get back to CSS defaults
  headers.forEach((header) => {
    header.style.width = "";
    header.style.minWidth = "";
    header.style.maxWidth = "";
    header.style.paddingLeft = "";
    header.style.paddingRight = "";
  });

  // Re-initialize defaultMinColumnWidths and defaultMaxColumnWidths from current CSS
  headers.forEach((header, index) => {
    const style = getComputedStyle(header);
    resizeState.defaultMinColumnWidths[index] =
      parseInt(style.minWidth) || resizeState.MIN_COLUMN_WIDTH;
    resizeState.defaultMaxColumnWidths[index] =
      parseInt(style.maxWidth) || resizeState.MAX_COLUMN_WIDTH;
  });

  // Recalculate and apply widths based on CSS values
  calculateAndApplyColumnWidths(grid, headers, resizeState.defaultColumnWidths);
  console.log("Column widths reset to CSS defaults:", resizeState.defaultColumnWidths);
  console.log("Min widths:", resizeState.defaultMinColumnWidths);
  console.log("Max widths:", resizeState.defaultMaxColumnWidths);

  // Update the state
  AppState.setColumnResizeState(resizeState);
  updateAllCellWidths();
}

function toggleGridTextWrap() {
  const grid = document.getElementById("endpoints-grid");
  const toggleButton = document.getElementById("toggle-text-wrap-btn");
  if (!grid || !toggleButton) return;

  grid.classList.toggle("text-wrap");
  const isTextWrapEnabled = grid.classList.contains("text-wrap");

  if (isTextWrapEnabled) {
    toggleButton.textContent = "Disable Text Wrap";
    toggleButton.classList.add("active");
  } else {
    toggleButton.textContent = "Enable Text Wrap";
    toggleButton.classList.remove("active");
  }

  updateAllCellWidths();

  // Update the request parameters display format (comma-separated vs line breaks)
  updateRequestParametersDisplay(isTextWrapEnabled);
  console.log(`Text wrap ${isTextWrapEnabled ? "enabled" : "disabled"}.`);
}

// Update the display of request parameters based on text wrap status
function updateRequestParametersDisplay(isTextWrapEnabled) {
  const rows = document.querySelectorAll("#grid-body tr");

  rows.forEach((row) => {
    // The request parameters cell is the last cell in each row
    const requestParamsCell = row.querySelector("td:last-child");
    if (!requestParamsCell) return;

    // Get the current endpoint data for this row
    const rowIndex = Array.from(rows).indexOf(row);
    const filteredData = AppState.getFilteredData();
    if (rowIndex < 0 || rowIndex >= filteredData.length) return;

    const item = filteredData[rowIndex];
    const endpoint =
      item.diffType === "Removed"
        ? item.oldValue
        : item.newValue || item.oldValue;
    if (
      !endpoint ||
      !endpoint.requestParameters ||
      endpoint.requestParameters.length === 0
    )
      return;

    // Update the display format based on text wrap setting
    if (isTextWrapEnabled) {
      requestParamsCell.innerHTML = endpoint.requestParameters.join("<br>");
    } else {
      requestParamsCell.textContent = endpoint.requestParameters.join(", ");
    }
  });
}

// Export functions for use by other modules
window.ColumnResizer = {
  initTableColumnResizing,
  calculateAndApplyColumnWidths,
  handleColumnMouseDown,
  handleColumnMouseMove,
  handleColumnMouseUp,
  autoFitColumnToContent,
  updateAllCellWidths,
  resetColumnWidthsToDefaults,
  toggleGridTextWrap,
  updateRequestParametersDisplay
};
