// Security Review History Modal
// This file manages the security review history display modal

// Show security review history modal for an endpoint with enhanced error handling
function showSecurityReviewHistoryModal(endpoint) {
  console.log("showSecurityReviewHistoryModal called with:", endpoint);
  
  try {
    if (!endpoint) {
      console.error("Cannot show security review history modal: No endpoint provided");
      if (typeof showError === 'function') {
        showError("Cannot display review history: No endpoint selected", {
          persistent: false,
          showInGrid: false
        });
      }
      return;
    }

    // Get the modal element
    const modal = document.getElementById("securityReviewHistoryModal");
    if (!modal) {
      console.error("Security review history modal not found in DOM");
      if (typeof showError === 'function') {
        showError("Review history interface not available", {
          details: "The security review history modal is missing from the page. Please refresh and try again.",
          persistent: true,
          showInGrid: false
        });
      }
      return;
    }

    // Check if Bootstrap is available
    if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
      console.error("Bootstrap Modal is not available");
      if (typeof showError === 'function') {
        showError("Modal system not available", {
          details: "Bootstrap Modal library is not loaded. Please refresh the page and try again.",
          persistent: true,
          showInGrid: false
        });
      }
      return;
    }

    // Get the Bootstrap modal instance
    const modalInstance = bootstrap.Modal.getOrCreateInstance(modal);

    // Extract endpoint information with fallbacks
    const httpMethod = endpoint.httpMethod || 
      (endpoint.httpMethods && Array.isArray(endpoint.httpMethods) ? endpoint.httpMethods.join(", ") : null) ||
      (endpoint.newValue && endpoint.newValue.httpMethod) || 
      (endpoint.newValue && endpoint.newValue.httpMethods && Array.isArray(endpoint.newValue.httpMethods) ? endpoint.newValue.httpMethods.join(", ") : null) ||
      (endpoint.oldValue && endpoint.oldValue.httpMethod) || 
      (endpoint.oldValue && endpoint.oldValue.httpMethods && Array.isArray(endpoint.oldValue.httpMethods) ? endpoint.oldValue.httpMethods.join(", ") : null) ||
      "Unknown";
      
    const route = endpoint.route || 
      (endpoint.newValue && endpoint.newValue.route) || 
      (endpoint.oldValue && endpoint.oldValue.route) || 
      "Unknown Route";

    // Set endpoint information in the modal
    const httpMethodElement = document.getElementById("history-http-method");
    const routeElement = document.getElementById("history-route");
    
    if (httpMethodElement) {
      httpMethodElement.textContent = httpMethod;
    }
    
    if (routeElement) {
      routeElement.textContent = route;
    }
    
    // Load and display reviews for this endpoint
    loadReviewsForEndpoint(endpoint.id);
    
    // Show the modal
    modalInstance.show();
    
  } catch (error) {
    console.error("Error showing security review history modal:", error);
    if (typeof showError === 'function') {
      showError("Failed to open review history", {
        details: error.message,
        persistent: true,
        showInGrid: false,
        allowRetry: true,
        retryCallback: 'retryShowHistoryModal'
      });
    }
  }
}

// Load and display all reviews for a specific endpoint with enhanced error handling
function loadReviewsForEndpoint(endpointId) {
  try {
    if (!endpointId) {
      throw new Error("Endpoint ID is required");
    }

    console.log(`Loading reviews for endpoint: ${endpointId}`);
    
    // Show loading state
    const tbody = document.getElementById("review-history-tbody");
    const noReviewsMessage = document.getElementById("no-reviews-message");
    const reviewHistoryContainer = document.getElementById("review-history-container");
    
    if (!tbody) {
      throw new Error("Review history table body not found");
    }
    
    // Show loading indicator
    tbody.innerHTML = `
      <tr>
        <td colspan="4" class="text-center p-4">
          <div class="d-flex align-items-center justify-content-center">
            <div class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></div>
            <span>Loading review history...</span>
          </div>
        </td>
      </tr>`;
    
    // Hide messages while loading
    if (noReviewsMessage) noReviewsMessage.classList.add("d-none");
    if (reviewHistoryContainer) reviewHistoryContainer.classList.remove("d-none");
    
    // Get all reviews for this endpoint
    let reviews;
    try {
      reviews = getSecurityReviewsForEndpoint(endpointId);
    } catch (error) {
      console.error("Error getting reviews for endpoint:", error);
      throw new Error(`Failed to retrieve reviews: ${error.message}`);
    }
    
    // Clear loading state
    tbody.innerHTML = "";
    
    // Show/hide elements based on whether there are reviews
    if (!reviews || reviews.length === 0) {
      if (noReviewsMessage) noReviewsMessage.classList.remove("d-none");
      if (reviewHistoryContainer) reviewHistoryContainer.classList.add("d-none");
      return;
    } else {
      if (noReviewsMessage) noReviewsMessage.classList.add("d-none");
      if (reviewHistoryContainer) reviewHistoryContainer.classList.remove("d-none");
    }
    
    // Create rows for each review with error handling
    const validRows = [];
    const errorRows = [];
    
    reviews.forEach((review, index) => {
      try {
        const row = createReviewTableRow(review, { includeEndpoint: false });
        validRows.push(row);
      } catch (error) {
        console.error(`Error creating row for review ${index}:`, error);
        errorRows.push({ index, error: error.message });
      }
    });
    
    // Add valid rows to table
    validRows.forEach(row => {
      tbody.appendChild(row);
    });
    
    // Show warning if some rows failed to create
    if (errorRows.length > 0) {
      console.warn(`Failed to create ${errorRows.length} review rows:`, errorRows);
      if (typeof showWarning === 'function') {
        showWarning(`Some review entries could not be displayed (${errorRows.length} of ${reviews.length})`, {
          details: "Please refresh the page or contact support if the problem persists.",
          persistent: false
        });
      }
    }
    
    // Set the initial sort (newest first) with error handling
    try {
      sortReviewHistoryTable("date", "desc");
      updateHistorySortButtonStates("date", "desc");
    } catch (error) {
      console.error("Error setting initial sort:", error);
    }
    
  } catch (error) {
    console.error("Error loading reviews for endpoint:", error);
    
    // Show error in the table
    const tbody = document.getElementById("review-history-tbody");
    if (tbody) {
      tbody.innerHTML = `
        <tr>
          <td colspan="4" class="text-center p-4 text-danger">
            <div class="d-flex align-items-center justify-content-center">
              <i class="bi bi-exclamation-triangle me-2"></i>
              <span>Error loading review history: ${error.message}</span>
            </div>
            <div class="mt-2">
              <button class="btn btn-sm btn-outline-primary" onclick="retryLoadReviews('${endpointId}')">
                <i class="bi bi-arrow-clockwise"></i> Retry
              </button>
            </div>
          </td>
        </tr>`;
    }
    
    // Also show toast notification
    if (typeof showError === 'function') {
      showError("Failed to load review history", {
        details: error.message,
        persistent: false,
        showInGrid: false
      });
    }
  }
}

// Initialize event listeners for the security review history modal
function initSecurityReviewHistoryModal() {
  console.log("Initializing security review history modal");
  
  // Initialize sort buttons
  initHistorySortButtons();
  
  console.log("Security review history modal initialized");
}

// Initialize sort buttons for history modal
function initHistorySortButtons() {
  // Use event delegation for sort buttons
  document.addEventListener("click", function(e) {
    const sortBtn = e.target.closest(".sort-btn");
    if (!sortBtn) return;
    
    const sortBy = sortBtn.getAttribute("data-sort-by");
    let sortOrder = sortBtn.getAttribute("data-sort-order") || "asc";
    
    // Toggle sort order if clicking the same button again
    if (sortBtn.classList.contains("active")) {
      sortOrder = sortOrder === "asc" ? "desc" : "asc";
      sortBtn.setAttribute("data-sort-order", sortOrder);
    }
    
    // Sort the table
    sortReviewHistoryTable(sortBy, sortOrder);
    
    // Update sort button states
    updateHistorySortButtonStates(sortBy, sortOrder);
  });
  
  // Also add click handlers for table headers
  document.addEventListener("click", function(e) {
    const headerCell = e.target.closest("#review-history-table th");
    if (!headerCell) return;
    
    // Determine which column was clicked
    const headerIndex = Array.from(headerCell.parentNode.children).indexOf(headerCell);
    let sortBy;
    
    // Map header index to sort column
    switch (headerIndex) {
      case 0:
        sortBy = "date";
        break;
      case 1:
        sortBy = "reviewer";
        break;
      case 2:
        sortBy = "status";
        break;
      default:
        return; // Don't sort on notes column
    }
    
    // Find the corresponding sort button
    const sortBtn = document.querySelector(`.sort-btn[data-sort-by="${sortBy}"]`);
    if (!sortBtn) return;
    
    // Get current sort order
    let sortOrder = sortBtn.getAttribute("data-sort-order") || "asc";
    
    // Toggle sort order if this column is already active
    if (sortBtn.classList.contains("active")) {
      sortOrder = sortOrder === "asc" ? "desc" : "asc";
      sortBtn.setAttribute("data-sort-order", sortOrder);
    }
    
    // Sort the table
    sortReviewHistoryTable(sortBy, sortOrder);
    
    // Update sort button states
    updateHistorySortButtonStates(sortBy, sortOrder);
  });
}

// Update sort button states for history modal
function updateHistorySortButtonStates(activeSortBy, activeSortOrder) {
  const sortButtons = document.querySelectorAll(".sort-btn");
  
  sortButtons.forEach(button => {
    const sortBy = button.getAttribute("data-sort-by");
    
    // Reset all buttons
    button.classList.remove("active");
    button.textContent = button.textContent.replace(" ↑", "").replace(" ↓", "");
    
    // Update the active button
    if (sortBy === activeSortBy) {
      button.classList.add("active");
      button.setAttribute("data-sort-order", activeSortOrder);
      button.textContent = button.textContent + (activeSortOrder === "asc" ? " ↑" : " ↓");
    }
  });
}

// Retry functions for error recovery
window.retryLoadReviews = function(endpointId) {
  loadReviewsForEndpoint(endpointId);
};

window.retryShowHistoryModal = function() {
  if (window.currentEndpoint) {
    showSecurityReviewHistoryModal(window.currentEndpoint);
  } else {
    if (typeof showWarning === 'function') {
      showWarning("Cannot retry: No endpoint selected", {
        persistent: false
      });
    }
  }
};

// Export for browser environment
if (typeof window !== "undefined") {
  window.showSecurityReviewHistoryModal = showSecurityReviewHistoryModal;
  window.loadReviewsForEndpoint = loadReviewsForEndpoint;
  window.initSecurityReviewHistoryModal = initSecurityReviewHistoryModal;
  window.initHistorySortButtons = initHistorySortButtons;
  window.updateHistorySortButtonStates = updateHistorySortButtonStates;
}

// Export for module environment
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    showSecurityReviewHistoryModal,
    loadReviewsForEndpoint,
    initSecurityReviewHistoryModal,
    initHistorySortButtons,
    updateHistorySortButtonStates
  };
}
