---
inclusion: fileMatch
fileMatchPattern: ['**/*.js', '**/*.ts']
---

# JavaScript Conventions

## Naming Conventions

- Use camelCase for variables and functions
- Use PascalCase for classes and constructors
- Use UPPER_SNAKE_CASE for module-level constants
- Use descriptive names that reflect component purpose

## Code Organization

- Single file architecture for this vanilla JS application
- Global state variables declared at module level
- Event listeners initialized in dedicated `initEventListeners()` function
- DOM queries cached where possible for performance optimization
- Modular functions organized by feature area

## DOM Manipulation

- Use vanilla JavaScript with no frameworks
- Cache DOM elements in variables when accessed multiple times
- Use semantic HTML5 elements with Bootstrap classes
- Prefer `addEventListener` over inline event handlers

## Data Handling

- Expect JSON data with `diffType`, `newValue`, `oldValue` properties
- Use consistent property access patterns
- <PERSON><PERSON> missing or undefined data gracefully
- Maintain global state for filters, sorting, and UI state

## Performance Practices

- Minimize DOM queries by caching elements
- Use event delegation for dynamic content
- Avoid unnecessary re-renders of large datasets
- Implement efficient filtering and sorting algorithms

## Error Handling

- Validate data structure before processing
- Provide user feedback for loading states
- Handle file loading errors gracefully
- Use try-catch blocks for potentially failing operations
