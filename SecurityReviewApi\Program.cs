// Add this using statement at the top
using Microsoft.OpenApi.Models;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

// --- Add CORS Policy (as before) ---
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigin",
        builder =>
        {
            builder.WithOrigins("http://localhost:5500" ) // Adjust to your frontend's origin
                   .AllowAnyHeader()
                   .AllowAnyMethod();
        });
});

builder.Services.AddControllers();

// --- 1. Add Swagger Generator to the services collection ---
builder.Services.AddEndpointsApiExplorer(); // This is often already here
builder.Services.AddSwaggerGen(options =>
{
    options.SwaggerDoc("v1", new OpenApiInfo
    {
        Version = "v1",
        Title = "Security Review API",
        Description = "An ASP.NET Core Web API for managing security review data.",
        Contact = new OpenApiContact
        {
            Name = "Example Contact",
            Url = new Uri("https://example.com/contact" )
        }
    });
});


var app = builder.Build();

// --- 2. Enable Swagger middleware in the HTTP request pipeline ---
// This should be done in the development environment for security.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(options =>
    {
        options.SwaggerEndpoint("/swagger/v1/swagger.json", "Security Review API v1");
        // To serve the Swagger UI at the app's root (e.g., https://localhost:7123/ ), set the RoutePrefix to an empty string.
        options.RoutePrefix = string.Empty; 
    });
}


app.UseHttpsRedirection();

app.UseCors("AllowSpecificOrigin"); // Apply CORS policy

app.UseAuthorization();

app.MapControllers();

app.Run();
