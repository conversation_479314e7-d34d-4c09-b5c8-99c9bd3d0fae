# Project Structure

## Directory Organization

```text
/
├── index.html              # Main application entry point
├── css/
│   └── styles.css         # All application styles with CSS variables
├── js/
│   └── app.js            # Main application logic and event handlers
├── data/
│   └── anon.json         # Sample endpoint data (anonymized)
├── scripts/
│   └── anon.js           # Data anonymization utility script
└── .kiro/                # Kiro IDE configuration
    └── steering/         # Project steering documents
```

## File Responsibilities

### HTML Structure

- **index.html** - Single page application with all UI components defined
- Uses semantic HTML5 elements with Bootstrap classes
- Contains modal dialogs and tooltip elements

### CSS Organization

- **styles.css** - Monolithic stylesheet with CSS custom properties
- Organized by component sections (grid, filters, detail view, etc.)
- Responsive design with mobile-first approach
- Uses CSS variables for theming and consistent spacing

### JavaScript Architecture

- **app.js** - Single file containing all application logic
- Event-driven architecture with DOM manipulation
- Global state management for filters, sorting, and UI state
- Modular functions organized by feature area

### Data Management

- **data/** - Contains JSON files with endpoint information
- Expected format: objects with `diffType`, `newValue`, `oldValue` properties
- **scripts/** - Utility scripts for data processing and anonymization

## Coding Conventions

### CSS

- Use CSS custom properties (variables) defined in `:root`
- BEM-like naming for component-specific classes
- Responsive breakpoints at 768px for mobile
- Consistent use of flexbox for layouts

### JavaScript

- Camel case for variables and functions
- Global state variables declared at module level
- Event listeners initialized in `initEventListeners()`
- DOM queries cached where possible for performance

### File Naming

- Kebab-case for CSS classes
- camelCase for JavaScript identifiers
- Descriptive names reflecting component purpose
