<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EndpointInfo Explorer</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64,AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==">
    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Bootstrap Icons -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css"
      rel="stylesheet"
    />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css" />
    <!-- Security Review CSS -->
    <link rel="stylesheet" href="css/security-review-styles.css" />
  </head>

  <body>
    <!-- Main Application Container -->
    <div class="app-container">
      <!-- Left Menu - Will be loaded dynamically -->
      
      <!-- Main Content Area -->
      <div class="main-area">
        <!-- Top Menu - Will be loaded dynamically -->
        
        <!-- Main Content (Grid) - Will be loaded dynamically -->
        
      </div>

      <!-- Detail View - Will be loaded dynamically -->
      
    </div>

    <!-- Tooltip for MouseOver -->
    <div class="tooltip" id="endpoint-tooltip"></div>

    <!-- All Modals will be loaded dynamically and appended to body -->

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <!-- Component Loader - Load this first -->
    <script src="js/component-loader.js"></script>
    
    <!-- Security Review System - Modular Components -->
    <script src="js/security-review/data-models.js"></script>
    <script src="js/security-review/review-utils.js"></script>
    <script src="js/security-review/review-data-manager.js"></script>
    <script src="js/security-review/review-ui-components.js"></script>
    <script src="js/security-review/review-modal-handler.js"></script>
    <script src="js/security-review/history-modal.js"></script>
    <script src="js/security-review/history-table.js"></script>
    <script src="js/security-review/search-modal.js"></script>
    <script src="js/security-review/search-filters.js"></script>
    <script src="js/security-review/security-review-main.js"></script>
    <script src="js/security-review/review-fixes.js"></script>
    
    <!-- Application Modules (loaded in dependency order) -->
    <!-- Core State Management -->
    <script src="js/app-state.js"></script>
    <!-- Notifications and Error Handling -->
    <script src="js/notifications.js"></script>
    <!-- Layout Management -->
    <script src="js/layout-manager.js"></script>
    <!-- Data Loading and Processing -->
    <script src="js/data-loader.js"></script>
    <!-- Column Resizing -->
    <script src="js/column-resizer.js"></script>
    <!-- Filtering and Sorting -->
    <script src="js/filters-sort.js"></script>
    <!-- Grid Rendering -->
    <script src="js/grid-manager.js"></script>
    <!-- Detail View Management -->
    <script src="js/detail-view.js"></script>
    <!-- Main Application -->
    <script src="js/app-main.js"></script>
    
    <!-- Debug scripts for testing (remove in production) -->
    <script src="final-button-test.js"></script>
    <script src="debug-security-issues.js"></script>
    <script src="test-review-submission.js"></script>

    <script>
      // Initialize components when DOM is ready
      document.addEventListener('DOMContentLoaded', function() {
        // Load all components first
        initializeComponents();
      });
    </script>
  </body>
</html>
