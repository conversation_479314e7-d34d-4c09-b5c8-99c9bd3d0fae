using System.Text.Json.Serialization;

namespace SecurityReviewApi.Models
{
    public class SecurityReview
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [<PERSON>sonPropertyName("endpointId")]
        public string EndpointId { get; set; }

        [Json<PERSON>ropertyName("reviewDateTime")]
        public DateTime ReviewDateTime { get; set; }

        [Json<PERSON>ropertyName("reviewerUsername")]
        public string ReviewerUsername { get; set; }

        [Json<PERSON>ropertyName("reviewNotes")]
        public string ReviewNotes { get; set; }

        [JsonPropertyName("securityStatus")]
        public string SecurityStatus { get; set; }
    }
}
