@echo off
REM Build standalone version of API Endpoint Explorer
REM This creates a single HTML file with all components embedded

echo Building standalone version...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is not installed or not in PATH.
    echo Please install Python 3 and try again.
    pause
    exit /b 1
)

python build-standalone.py

echo.
echo Build complete! You can now open index-standalone.html directly in your browser.
pause
