// Advanced Security Review Search Modal
// This file manages the advanced search interface

// Global state for search functionality
let currentSearchResults = [];
let currentSearchCriteria = {};

// Show the advanced search modal
function showAdvancedSearchModal() {
  console.log("Showing advanced security search modal");
  
  // Get the modal element
  const modal = document.getElementById("advancedSecuritySearchModal");
  console.log("Modal element found:", !!modal);
  
  if (!modal) {
    console.error("Advanced search modal not found in DOM");
    if (typeof showError === 'function') {
      showError("Advanced search interface not available. Please refresh the page.");
    }
    return;
  }
  
  // Check if Bootstrap is available
  if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
    console.error("Bootstrap Modal is not available");
    if (typeof showError === 'function') {
      showError("Modal system not available. Please refresh the page.");
    }
    return;
  }
  
  // Get the Bootstrap modal instance
  const modalInstance = bootstrap.Modal.getOrCreateInstance(modal);
  
  // Initialize the form with default values
  initializeSearchForm();
  
  // Execute initial search to show all reviews
  executeAdvancedSearch();
  
  // Show the modal
  modalInstance.show();
}

// Initialize the search form with default values
function initializeSearchForm() {
  // Clear text inputs
  const endpointInput = document.getElementById("search-endpoint");
  const reviewerInput = document.getElementById("search-reviewer");
  const notesInput = document.getElementById("search-notes");
  const dateFromInput = document.getElementById("search-date-from");
  const dateToInput = document.getElementById("search-date-to");
  
  if (endpointInput) endpointInput.value = "";
  if (reviewerInput) reviewerInput.value = "";
  if (notesInput) notesInput.value = "";
  if (dateFromInput) dateFromInput.value = "";
  if (dateToInput) dateToInput.value = "";
  
  // Check all security status checkboxes by default
  const statusCheckboxes = document.querySelectorAll("#advancedSearchForm input[type='checkbox']");
  statusCheckboxes.forEach(checkbox => {
    checkbox.checked = true;
  });
  
  // Initialize results count to 0
  const resultsCount = document.getElementById("results-count");
  if (resultsCount) {
    resultsCount.textContent = "0";
    console.log("📊 Initialized search results count to 0");
  }
}

// Execute the advanced search with enhanced error handling
function executeAdvancedSearch() {
  console.log("Executing advanced search");
  
  try {
    // Get search criteria from the form
    const criteria = getSearchCriteria();
    currentSearchCriteria = criteria;
    
    console.log("Search criteria:", criteria);
    
    // Get all security reviews
    const allReviews = getAllSecurityReviews();
    console.log(`Total reviews available: ${allReviews.length}`);
    
    // Filter reviews by criteria
    const filteredReviews = filterReviewsByCriteria(allReviews, criteria);
    console.log(`Filtered reviews: ${filteredReviews.length}`);
    
    // Store current results
    currentSearchResults = filteredReviews;
    
    // Display the results
    displaySearchResults(filteredReviews);
    
  } catch (error) {
    console.error("Error executing advanced search:", error);
    displaySearchError(error.message);
  }
}

// Display search results in the table with enhanced error handling
function displaySearchResults(results) {
  try {
    const tbody = document.getElementById("search-results-tbody");
    const noResultsDiv = document.getElementById("no-search-results");
    const resultsContainer = document.getElementById("search-results-container");
    const resultsCount = document.getElementById("results-count");
    
    if (!tbody) {
      throw new Error("Search results table body not found");
    }
    
    // Clear existing results
    tbody.innerHTML = "";
    
    // Update results count
    if (resultsCount) {
      resultsCount.textContent = `${results.length}`;
      console.log(`✅ Updated search results count to: ${results.length}`);
    } else {
      console.warn("❌ Results count element with ID 'results-count' not found");
    }
    
    // Show/hide elements based on results
    if (results.length === 0) {
      if (noResultsDiv) noResultsDiv.classList.remove("d-none");
      if (resultsContainer) resultsContainer.classList.add("d-none");
      return;
    } else {
      if (noResultsDiv) noResultsDiv.classList.add("d-none");
      if (resultsContainer) resultsContainer.classList.remove("d-none");
    }
    
    // Create rows for each result
    results.forEach((review, index) => {
      try {
        const row = createReviewTableRow(review, { 
          includeEndpoint: true, 
          enableRowClick: true 
        });
        tbody.appendChild(row);
      } catch (error) {
        console.error(`Error creating search result row ${index}:`, error);
      }
    });
    
    // Set default sort (newest first)
    setTimeout(() => {
      sortSearchResults("date", "desc");
      updateSearchResultsSortButtonStates("date", "desc");
    }, 100);
    
  } catch (error) {
    console.error("Error displaying search results:", error);
    displaySearchError(`Failed to display results: ${error.message}`);
  }
}

// Display search error in the results area
function displaySearchError(errorMessage) {
  const tbody = document.getElementById("search-results-tbody");
  const noResultsDiv = document.getElementById("no-search-results");
  const resultsContainer = document.getElementById("search-results-container");
  
  if (tbody) {
    tbody.innerHTML = `
      <tr>
        <td colspan="6" class="text-center p-4 text-danger">
          <div class="d-flex align-items-center justify-content-center">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <span>Error: ${errorMessage}</span>
          </div>
          <div class="mt-2">
            <button class="btn btn-sm btn-outline-primary" onclick="retryAdvancedSearch()">
              <i class="bi bi-arrow-clockwise"></i> Retry Search
            </button>
          </div>
        </td>
      </tr>`;
  }
  
  if (noResultsDiv) noResultsDiv.classList.add("d-none");
  if (resultsContainer) resultsContainer.classList.remove("d-none");
}

// Clear search filters
function clearSearchFilters() {
  console.log("Clearing search filters");
  
  // Clear text inputs
  const endpointInput = document.getElementById("search-endpoint");
  const reviewerInput = document.getElementById("search-reviewer");
  const notesInput = document.getElementById("search-notes");
  const dateFromInput = document.getElementById("search-date-from");
  const dateToInput = document.getElementById("search-date-to");
  
  if (endpointInput) endpointInput.value = "";
  if (reviewerInput) reviewerInput.value = "";
  if (notesInput) notesInput.value = "";
  if (dateFromInput) dateFromInput.value = "";
  if (dateToInput) dateToInput.value = "";
  
  // Check all security status checkboxes
  selectAllSecurityStatuses();
  
  // Execute search to show all results
  executeAdvancedSearch();
}

// Select all security status checkboxes
function selectAllSecurityStatuses() {
  const statusCheckboxes = document.querySelectorAll("#advancedSearchForm input[type='checkbox']");
  statusCheckboxes.forEach(checkbox => {
    checkbox.checked = true;
  });
}

// Clear all security status checkboxes
function clearAllSecurityStatuses() {
  const statusCheckboxes = document.querySelectorAll("#advancedSearchForm input[type='checkbox']");
  statusCheckboxes.forEach(checkbox => {
    checkbox.checked = false;
  });
}

// Export search results to CSV with enhanced error handling
function exportSearchResults() {
  console.log("Exporting search results");
  
  try {
    if (!currentSearchResults || currentSearchResults.length === 0) {
      if (typeof showWarning === 'function') {
        showWarning("No search results to export", {
          details: "Please run a search first to get results to export.",
          persistent: false
        });
      }
      return;
    }
    
    // Generate CSV content
    const csvContent = generateCSVContent(currentSearchResults);
    
    // Create and trigger download
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", `security_review_search_results_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      if (typeof showSuccessMessage === 'function') {
        showSuccessMessage(`Exported ${currentSearchResults.length} search results to CSV`);
      }
    } else {
      throw new Error("File download not supported in this browser");
    }
    
  } catch (error) {
    console.error("Error exporting search results:", error);
    if (typeof showError === 'function') {
      showError("Failed to export search results", {
        details: error.message,
        persistent: false,
        allowRetry: true,
        retryCallback: 'retryExportResults'
      });
    }
  }
}

// Initialize event listeners for the advanced search modal
function initAdvancedSearchModal() {
  console.log("Initializing advanced search modal");
  
  // These functions will be called from the main initialization
  initSearchEventListeners();
  initSearchFormHandlers();
  initSearchResultsHandlers();
  
  console.log("Advanced search modal initialized");
}

// Initialize event listeners for the advanced search button
function initSearchEventListeners() {
  // Advanced search button click handler
  const advancedSearchBtn = document.getElementById("advanced-search-btn");
  console.log("Advanced search button found:", !!advancedSearchBtn);
  
  if (advancedSearchBtn) {
    advancedSearchBtn.addEventListener("click", showAdvancedSearchModal);
    console.log("Advanced search event listener attached");
  }
}

// Initialize form handlers for the search modal
function initSearchFormHandlers() {
  // Execute search button
  const executeSearchBtn = document.getElementById("execute-search");
  if (executeSearchBtn) {
    executeSearchBtn.addEventListener("click", executeAdvancedSearch);
  }
  
  // Clear search button
  const clearSearchBtn = document.getElementById("clear-search");
  if (clearSearchBtn) {
    clearSearchBtn.addEventListener("click", clearSearchFilters);
  }
  
  // Export results button
  const exportResultsBtn = document.getElementById("export-results");
  if (exportResultsBtn) {
    exportResultsBtn.addEventListener("click", exportSearchResults);
  }
  
  // Select all statuses button
  const selectAllStatusesBtn = document.getElementById("select-all-statuses");
  if (selectAllStatusesBtn) {
    selectAllStatusesBtn.addEventListener("click", selectAllSecurityStatuses);
  }
  
  // Clear all statuses button
  const clearAllStatusesBtn = document.getElementById("clear-all-statuses");
  if (clearAllStatusesBtn) {
    clearAllStatusesBtn.addEventListener("click", clearAllSecurityStatuses);
  }
  
  // Real-time search on form input changes
  const searchForm = document.getElementById("advancedSearchForm");
  if (searchForm) {
    const inputs = searchForm.querySelectorAll("input");
    inputs.forEach(input => {
      const debouncedSearch = debounce(executeAdvancedSearch, 500);
      input.addEventListener("input", debouncedSearch);
    });
  }
}

// Initialize results table handlers
function initSearchResultsHandlers() {
  // Sort buttons for results table
  document.addEventListener("click", function(e) {
    const sortBtn = e.target.closest(".results-sort-btn");
    if (!sortBtn) return;
    
    const sortBy = sortBtn.getAttribute("data-sort-by");
    let sortOrder = sortBtn.getAttribute("data-sort-order") || "asc";
    
    // Toggle sort order if clicking the same button again
    if (sortBtn.classList.contains("active")) {
      sortOrder = sortOrder === "asc" ? "desc" : "asc";
      sortBtn.setAttribute("data-sort-order", sortOrder);
    }
    
    // Sort the results
    sortSearchResults(sortBy, sortOrder);
    
    // Update sort button states
    updateSearchResultsSortButtonStates(sortBy, sortOrder);
  });
  
  // Table header click handlers
  document.addEventListener("click", function(e) {
    const headerCell = e.target.closest("#search-results-table th");
    if (!headerCell) return;
    
    // Determine which column was clicked
    const headerIndex = Array.from(headerCell.parentNode.children).indexOf(headerCell);
    let sortBy;
    
    // Map header index to sort column
    switch (headerIndex) {
      case 0:
        sortBy = "date";
        break;
      case 1:
        sortBy = "endpoint";
        break;
      case 2:
        sortBy = "method";
        break;
      case 3:
        sortBy = "reviewer";
        break;
      case 4:
        sortBy = "status";
        break;
      default:
        return; // Don't sort on notes column
    }
    
    // Find the corresponding sort button
    const sortBtn = document.querySelector(`.results-sort-btn[data-sort-by="${sortBy}"]`);
    if (!sortBtn) return;
    
    // Get current sort order
    let sortOrder = sortBtn.getAttribute("data-sort-order") || "asc";
    
    // Toggle sort order if this column is already active
    if (sortBtn.classList.contains("active")) {
      sortOrder = sortOrder === "asc" ? "desc" : "asc";
      sortBtn.setAttribute("data-sort-order", sortOrder);
    }
    
    // Sort the results
    sortSearchResults(sortBy, sortOrder);
    
    // Update sort button states
    updateSearchResultsSortButtonStates(sortBy, sortOrder);
  });
}

// Debounce function for real-time search
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Retry functions for error recovery
window.retryAdvancedSearch = function() {
  executeAdvancedSearch();
};

window.retryExportResults = function() {
  exportSearchResults();
};

// Export for browser environment
if (typeof window !== "undefined") {
  window.currentSearchResults = currentSearchResults;
  window.currentSearchCriteria = currentSearchCriteria;
  window.showAdvancedSearchModal = showAdvancedSearchModal;
  window.initializeSearchForm = initializeSearchForm;
  window.executeAdvancedSearch = executeAdvancedSearch;
  window.displaySearchResults = displaySearchResults;
  window.displaySearchError = displaySearchError;
  window.clearSearchFilters = clearSearchFilters;
  window.selectAllSecurityStatuses = selectAllSecurityStatuses;
  window.clearAllSecurityStatuses = clearAllSecurityStatuses;
  window.exportSearchResults = exportSearchResults;
  window.initAdvancedSearchModal = initAdvancedSearchModal;
  window.initSearchEventListeners = initSearchEventListeners;
  window.initSearchFormHandlers = initSearchFormHandlers;
  window.initSearchResultsHandlers = initSearchResultsHandlers;
  window.debounce = debounce;
}

// Export for module environment
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    currentSearchResults,
    currentSearchCriteria,
    showAdvancedSearchModal,
    initializeSearchForm,
    executeAdvancedSearch,
    displaySearchResults,
    displaySearchError,
    clearSearchFilters,
    selectAllSecurityStatuses,
    clearAllSecurityStatuses,
    exportSearchResults,
    initAdvancedSearchModal,
    initSearchEventListeners,
    initSearchFormHandlers,
    initSearchResultsHandlers,
    debounce
  };
}
