/* Security Review Styles */
/* This file contains all CSS styles for the security review functionality */

/* Make security status cells clickable */
.security-status {
  cursor: pointer;
}

.security-status:hover {
  opacity: 0.8;
}

/* Make review history table headers clickable without changing background */
#review-history-table th {
  cursor: pointer;
  position: relative;
}

/* Add a subtle indicator on hover instead of changing background */
#review-history-table th:hover::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--color-primary);
}

/* Make the security review history modal 60% width */
#securityReviewHistoryModal .modal-dialog {
  max-width: 60%;
  width: 60%;
}

/* Ensure the modal is properly centered */
#securityReviewHistoryModal .modal-dialog {
  margin-left: auto;
  margin-right: auto;
}

/* Make the review history table resizable */
.table-responsive {
  resize: both;
  overflow: auto;
  min-height: 200px;
  min-width: 300px;
  border: 1px solid #dee2e6;
  position: relative;
}

/* Add resize handle indicator */
.table-responsive::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background-image: linear-gradient(135deg, #ccc 25%, transparent 25%, transparent 50%, #ccc 50%, #ccc 75%, transparent 75%, transparent);
  background-size: 10px 10px;
  cursor: nwse-resize;
}

/* Make table columns resizable */
#review-history-table {
  table-layout: fixed;
  width: 100%;
}

/* Use colgroup for resizable columns */
#review-history-table colgroup col {
  width: auto;
}

/* Default column widths */
#review-history-table colgroup col:nth-child(1) {
  width: 20%;
}

#review-history-table colgroup col:nth-child(2) {
  width: 15%;
}

#review-history-table colgroup col:nth-child(3) {
  width: 15%;
}

#review-history-table colgroup col:nth-child(4) {
  width: 50%;
}

/* Column resizer styling */
.column-resizer {
  position: absolute;
  top: 0;
  right: 0;
  width: 5px;
  height: 100%;
  cursor: col-resize;
  z-index: 1;
}

.column-resizer:hover,
.column-resizer.active {
  background-color: var(--color-primary);
}

/* Prevent text selection during resize */
.resize-active {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Improved column resizer visibility */
#review-history-table th .column-resizer {
  position: absolute;
  top: 0;
  right: 0;
  width: 6px;
  height: 100%;
  cursor: col-resize;
  background-color: transparent;
  z-index: 100;
}

#review-history-table th .column-resizer:hover {
  background-color: var(--color-primary);
  opacity: 0.7;
}

#review-history-table th .column-resizer.active {
  background-color: var(--color-primary);
  opacity: 1;
}

/* Fix for security review grid column resizing */
#endpoints-grid th .column-resizer {
  position: absolute;
  top: 0;
  right: -3px;
  width: 6px;
  height: 100%;
  cursor: col-resize;
  z-index: 100;
}

#endpoints-grid th .column-resizer:hover {
  background-color: var(--color-primary);
  opacity: 0.7;
}

#endpoints-grid th .column-resizer.active {
  background-color: var(--color-primary);
  opacity: 1;
}

/* Security status badge styles */
.security-status-compliant {
  color: #155724;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-weight: 500;
}

.security-status-non-compliant {
  color: #721c24;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-weight: 500;
}

.security-status-risk-accepted {
  color: #856404;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-weight: 500;
}

.security-status-under-review {
  color: #0c5460;
  background-color: #d1ecf1;
  border: 1px solid #bee5eb;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-weight: 500;
}

.security-status-critical {
  color: #721c24;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-weight: 500;
  animation: pulse 2s infinite;
}

.security-status-must-review {
  color: #ffffff !important;
  background-color: #ff0000 !important;
  border: 1px solid #cc0000 !important;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-weight: bold !important;
  animation: pulse 2s infinite;
}

.security-status-unknown {
  color: #6c757d;
  background-color: #e9ecef;
  border: 1px solid #dee2e6;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-weight: 500;
}

/* Pulse animation for critical statuses */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

/* Security action buttons */
.security-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.security-action-btn {
  background: none;
  border: 1px solid #dee2e6;
  color: #6c757d;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.security-action-btn:hover {
  background-color: #f8f9fa;
  border-color: #adb5bd;
  color: #495057;
}

.security-action-btn.add-review-btn:hover {
  background-color: #e3f2fd;
  border-color: #2196f3;
  color: #1976d2;
}

.security-action-btn.view-history-btn:hover {
  background-color: #f3e5f5;
  border-color: #9c27b0;
  color: #7b1fa2;
}

/* Review notes container */
.review-notes-container {
  position: relative;
}

.review-notes-preview,
.review-notes-content {
  max-height: 100px;
  overflow-y: auto;
  word-wrap: break-word;
}

.review-notes-toggle {
  padding: 0.125rem 0.25rem;
  font-size: 0.75rem;
  text-decoration: none;
  border: none;
  background: none;
  color: var(--bs-link-color);
  margin-top: 0.25rem;
}

.review-notes-toggle:hover {
  color: var(--bs-link-hover-color);
  text-decoration: underline;
}

/* Search results table */
#search-results-table {
  table-layout: fixed;
}

#search-results-table th {
  position: relative;
  cursor: pointer;
}

#search-results-table th:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

/* Sort button indicators */
.sort-btn.active {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  color: white;
}

.results-sort-btn.active {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  color: white;
}

/* Loading state styles */
.loading-state {
  position: relative;
  color: transparent !important;
}

.loading-state::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Toast notification positioning */
.toast-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}

/* Modal improvements */
.modal-dialog.modal-xl {
  max-width: 90%;
}

/* Form validation styles */
.was-validated .form-control:invalid {
  border-color: #dc3545;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:valid {
  border-color: #198754;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.7 1.7 3.06-3.06.94.94-4 4z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #securityReviewHistoryModal .modal-dialog {
    max-width: 95%;
    width: 95%;
  }
  
  .security-actions {
    flex-direction: column;
  }
  
  .security-action-btn {
    width: 100%;
    justify-content: center;
  }
  
  .table-responsive {
    min-width: 200px;
  }
}
