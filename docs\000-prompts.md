# Prompts

## 001 - Create a PRD from the existing implementation

- Perform an exhaustive analysis of this app and generate a complete prd file in markdown that will completely describe it.
- Include only small amounts of code only if absolutely necessary to help understanding.
- Include everything an expertly crafted prd should have, user stories, specs, acceptance criteria, functional specs, tech stack, everything.
- When you finish, I intend to validate it and then give it to you and ask you that you use it as guidelines to rewrite the application in React, with a backend and a frontend, and exactly the same functionality and appearance as it has now!
- Do not be lazy, do only as you are instructed!

## 002 - Add a new design to the application that will allow a user to enter endpoint security review and later review it

---

### 002-1 Ask to enhance the prompt

---

I will give you a prompt I want to use to add a new feature to an application I am currently developing.

Your only job is to enhance this prompt, to be more clear, well-structured, polished and technically fluent, while preserving the original intentions.

The prompt is as follows:

- Help me add a new feature to the application that will allow a user to select an endpoint and enter endpoint security review information, so that we have a track of the endpoints that have actually been reviewed for security issues and their respective security status.
- We will need to keep some new information such as the datetime, username, review notes, and security status.
- Of course all this information will need to be directly linked to the selected endpoint.
- Also it will need to be permanently stored as a new entry every time, the previous entry will remain unchanged.
- The application will always display the latest security review information for each endpoint. The Security status and the review date will need to be displayed in the grid, alongside the rest endpoint info.
- The user will have the option to click on a button perhaps and have the whole review history for the endpoint displayed in a new window. This window will be able to display all security information, including the notes, and allow for an easy browsing.
- The application will need to be able to display the information in a table format, with the ability to filter by endpoint and security status.

---

### 002-2 Enhanced version of the feature request prompt (by o4-mini)

---

Add “Endpoint Security Review” Tracking

Objective  
Allow users to record, store and browse security‐review history for each endpoint in our application, while always showing the most recent review on the main grid.

1. Data Model & Persistence  
 • Create a new, append-only “SecurityReview” record linked to an endpoint.  
 • Each review must capture:  
   – Review timestamp (datetime)  
   – Reviewer (username)  
   – Review notes (text)  
   – Security status (e.g. Approved / Pending / IssuesFound)  
 • Never overwrite or delete past reviews—always insert a new record.

2. Main Endpoint Grid  
 • Add two columns:  
   – Latest Security Status  
   – Last Review Date  
 • For each row, display the most recent review’s status and timestamp.

3. Add Security Review UI  
 • Add an “Add Security Review” button/icon on each endpoint row.  
 • Opens a modal or form pre‐selecting the chosen endpoint.  
 • Form fields: status (dropdown), notes (multiline text).  
 • Automatically capture current datetime and current user.

4. Review History View  
 • Add a “View Review History” button/icon on each endpoint row.  
 • Opens a new window or modal showing a table of all past reviews for that endpoint.  
 • Table columns: Review Date, Reviewer, Status, Notes.  
 • Provide filters for Endpoint and Security Status (and optionally date range).

5. Non-Functional Requirements  
 • Persist all reviews permanently in the database.  
 • Always default the main grid to show the latest review data.  
 • Ensure UI components follow our existing design system and handle validation/errors gracefully.  
 • Implement any necessary backend API endpoints and database migrations to support these features.

Please implement the above so we can track exactly which endpoints have been security-reviewed, when, by whom, and with what outcome.

---

### 002-3 Enhanced version of the feature request prompt (by gemini 2.5 flash thinking)

---

Here's an enhanced version of your prompt, designed to be clearer, more structured, and technically fluent:

## Feature Enhancement Request: Endpoint Security Review Tracking System

**Objective:** To implement a new feature within the application that enables comprehensive tracking of security reviews for individual endpoints. This system will allow users to record detailed security review information, maintain a complete audit trail of review statuses, and provide immediate visibility into the current security posture of each endpoint.

**Core Requirements:**

1. **Endpoint Security Review Entry Interface:**
    - Users must be able to select an existing endpoint from a predefined list or current view.
    - A dedicated input interface (e.g., a form or modal dialog) shall be provided for the submission of new security review information associated with the selected endpoint.

2. **Data Model Extension: `EndpointSecurityReview` Entity:**
    - A new data entity, `EndpointSecurityReview`, will be introduced to encapsulate the review-specific attributes. Each instance of this entity will represent a single security review event for an endpoint.
    - The `EndpointSecurityReview` entity will include, but not be limited to, the following attributes:
        - `reviewDateTime` (Timestamp): The precise date and time the security review was conducted.
        - `reviewerUsername` (String): The identifier of the user who performed and submitted the review.
        - `reviewNotes` (Text/Markdown): Comprehensive notes, findings, recommendations, and any relevant details from the security assessment.
        - `securityStatus` (Enum/String): The categorized outcome or status of the security review (e.g., "Compliant", "Non-Compliant", "Risk Accepted", "Under Review", "Critical Vulnerability"). This should ideally be a configurable enumeration.

3. **Data Relationship:**
    - Each `EndpointSecurityReview` record must establish a one-to-many relationship with the `Endpoint` entity. This implies that multiple security reviews can be associated with a single endpoint, but each review record belongs to only one endpoint. A foreign key (`endpointId`) will link `EndpointSecurityReview` instances to their respective `Endpoint` records.

4. **Audit Trail and Immutability:**
    - Every new security review submission will result in the creation of a distinct, immutable `EndpointSecurityReview` record.
    - Existing `EndpointSecurityReview` records associated with an endpoint shall remain unchanged upon the submission of a new review, ensuring a complete and tamper-evident historical audit trail.

5. **Endpoint Grid Integration (Latest Status Display):**
    - The primary application grid or table that lists endpoints will be enhanced to prominently display the most recent `securityStatus` and `reviewDateTime` from the latest `EndpointSecurityReview` record for each endpoint.
    - This information should be presented concisely alongside existing endpoint details.

6. **Full Review History Access:**
    - A user interface element (e.g., a "View History" button or link) will be implemented within the endpoint's detail view or adjacent to its entry in the main grid.
    - Upon activation, this element will trigger the display of a dedicated interface (e.g., a modal window or a new page) that presents the complete chronological history of all `EndpointSecurityReview` records for the selected endpoint.
    - This history view must clearly display all attributes of each `EndpointSecurityReview` record, including the full `reviewNotes`, in an easily browsable and sortable format.

7. **Search and Filtering Capabilities:**
    - The application must provide robust search and filtering functionalities for the `EndpointSecurityReview` data.
    - Users should be able to filter review entries based on:
        - Associated `Endpoint` (e.g., by endpoint name, ID, or other unique identifiers).
        - `securityStatus` (allowing selection of one or more statuses).
    - The results of such filters should be presented in a clear, tabular format.

---

### 002-4 Here is how I started the conversation and then pasted the enhanced version of the feature request prompt (by gemini 2.5 flash thinking)

---

Your task is to create requirements and comprehensive design in two different markdown files to help implement a new feature.

Here is the feature's description: (copy of 002-3 Enhanced version)
