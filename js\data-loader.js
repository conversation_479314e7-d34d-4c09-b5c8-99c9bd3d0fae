// Data Loading and Processing Module
// Handles loading endpoint data from files and processing security review data

// Constants
const FILENAME_ENDPOINTS = "data/anon.json";
const FILENAME_ENDPOINTS_SECURITY = "data/anon-security.json";


// Load default data file
function loadDefaultData() {
  // Load the included anon.json file
  fetch(FILENAME_ENDPOINTS)
    .then((response) => {
      if (!response.ok) {
        throw new Error("Failed to load default data file");
      }
      return response.json();
    })
    .then(async (data) => {
      await processData(data);
    })
    .catch((error) => {
      console.error("Error loading default data:", error);
      if (window.NotificationManager) {
        window.NotificationManager.showError(
          "Failed to load default data. Please try loading a file manually."
        );
      }
    });
}

// Process loaded data
async function processData(data) {
  console.log("Processing data...");
  console.log("Data received:", data);
  console.log("Data type:", typeof data);
  console.log("Data is null/undefined:", data == null);

  if (!data) {
    console.error("Data is null or undefined");
    if (window.NotificationManager) {
      window.NotificationManager.showError(
        "Invalid data format. No data received. Please ensure you are loading a valid JSON file."
      );
    }
    return;
  }

  console.log("Data keys:", Object.keys(data));
  console.log("Has endpointDiffs property:", "endpointDiffs" in data);
  console.log("endpointDiffs value:", data.endpointDiffs);
  console.log("endpointDiffs type:", typeof data.endpointDiffs);
  console.log("endpointDiffs is array:", Array.isArray(data.endpointDiffs));

  if (!("endpointDiffs" in data)) {
    console.error("No endpointDiffs property found");
    if (window.NotificationManager) {
      window.NotificationManager.showError(
        'Invalid data format. Expected JSON object with "endpointDiffs" property. Available properties: ' +
          Object.keys(data).join(", ")
      );
    }

    // Update row count to 0 when no data is available
    const rowCountSpan = document.getElementById("row-count");
    if (rowCountSpan) {
      rowCountSpan.textContent = "[0 rows]";
    }

    return;
  }

  if (!Array.isArray(data.endpointDiffs)) {
    console.error(
      "endpointDiffs is not an array, type:",
      typeof data.endpointDiffs
    );
    if (window.NotificationManager) {
      window.NotificationManager.showError(
        'Invalid data format. The "endpointDiffs" property must be an array. Found: ' +
          typeof data.endpointDiffs
      );
    }
    return;
  }

  console.log("endpointDiffs length:", data.endpointDiffs.length);

  if (data.endpointDiffs.length === 0) {
    console.warn("endpointDiffs array is empty");
    if (window.NotificationManager) {
      window.NotificationManager.showError(
        'The file contains no endpoint data. The "endpointDiffs" array is empty.'
      );
    }
    return;
  }

  // Enhanced validation with progress indication for large datasets
  const totalItems = data.endpointDiffs.length;
  const validationSampleSize = Math.min(10, totalItems); // Validate more items for better confidence
  const validationErrors = [];

  console.log(
    `Validating ${validationSampleSize} sample items from ${totalItems} total items...`
  );

  // Show progress for large datasets
  if (totalItems > 1000) {
    if (window.NotificationManager) {
      window.NotificationManager.showInfo(`Validating ${totalItems} endpoint records...`, {
        persistent: false,
      });
    }
  }

  // Validate sample items to ensure they have the expected structure
  for (let i = 0; i < validationSampleSize; i++) {
    const item = data.endpointDiffs[i];
    const itemErrors = [];

    if (!item || typeof item !== "object") {
      itemErrors.push(`Item ${i}: Expected object, found ${typeof item}`);
    } else {
      // Validate required properties
      if (!item.diffType) {
        itemErrors.push(`Item ${i}: Missing "diffType" property`);
      } else if (
        !["Added", "Removed", "Modified", "Unmodified"].includes(item.diffType)
      ) {
        itemErrors.push(
          `Item ${i}: Invalid diffType "${item.diffType}". Expected: Added, Removed, Modified, or Unmodified`
        );
      }

      // Check for required value properties based on diffType
      if (item.diffType === "Removed" && !item.oldValue) {
        itemErrors.push(
          `Item ${i}: Removed endpoint missing "oldValue" property`
        );
      }

      if (
        (item.diffType === "Added" || item.diffType === "Modified") &&
        !item.newValue
      ) {
        itemErrors.push(
          `Item ${i}: ${item.diffType} endpoint missing "newValue" property`
        );
      }

      // Validate endpoint structure within values
      const valueToCheck =
        item.diffType === "Removed" ? item.oldValue : item.newValue;
      if (valueToCheck && typeof valueToCheck === "object") {
        if (!valueToCheck.route && !valueToCheck.id) {
          itemErrors.push(
            `Item ${i}: Endpoint missing both "route" and "id" properties`
          );
        }
      }
    }

    if (itemErrors.length > 0) {
      validationErrors.push(...itemErrors);
    }
  }

  // Handle validation errors
  if (validationErrors.length > 0) {
    const errorMessage = `Data validation failed: Found ${
      validationErrors.length
    } error${validationErrors.length > 1 ? "s" : ""} in sample data`;
    if (window.NotificationManager) {
      window.NotificationManager.showError(errorMessage, {
        details:
          validationErrors.slice(0, 10).join("\\n") +
          (validationErrors.length > 10 ? "\\n... and more" : ""),
        persistent: true,
        allowRetry: false,
      });
    }
    return;
  }

  // Additional data quality checks
  const qualityWarnings = [];

  // Check for duplicate endpoints
  const routeMap = new Map();
  let duplicateCount = 0;

  for (let i = 0; i < Math.min(100, totalItems); i++) {
    const item = data.endpointDiffs[i];
    const endpoint =
      item.diffType === "Removed" ? item.oldValue : item.newValue;
    if (endpoint && endpoint.route) {
      const key = `${endpoint.route}-${endpoint.httpMethod || "unknown"}`;
      if (routeMap.has(key)) {
        duplicateCount++;
      } else {
        routeMap.set(key, true);
      }
    }
  }

  if (duplicateCount > 0) {
    qualityWarnings.push(
      `Found ${duplicateCount} potential duplicate endpoints in sample`
    );
  }

  // Show quality warnings if any
  if (qualityWarnings.length > 0) {
    if (window.NotificationManager) {
      window.NotificationManager.showWarning(`Data quality notice: ${qualityWarnings.join("; ")}`, {
        details: "This may be expected depending on your data source.",
        persistent: false,
      });
    }
  }

  console.log("Data validation passed successfully");
  AppState.setEndpointData(data.endpointDiffs);

  // Process security review data if present
  await processSecurityReviewData(data);

  // Show success message
  console.log(`Successfully loaded ${AppState.getEndpointData().length} endpoint records`);
  console.log(
    `Security reviews loaded: ${
      typeof securityReviews !== "undefined"
        ? securityReviews.length
        : "undefined"
    }`
  );

  // Clear any previous error displays in the grid
  const gridBody = document.getElementById("grid-body");
  if (gridBody) {
    gridBody.innerHTML = "";
  }

  // Apply filters to update the display
  if (window.FiltersSortManager) {
    window.FiltersSortManager.applyFilters();
  }
}

// Process security review data - load from separate security file
async function processSecurityReviewData(data) {
  console.log("Processing security review data...");

  // Try to load security reviews from separate file
  await loadSecurityReviewsFromFile();

  // Perform data migration for existing endpoints without security review data
  try {
    migrateEndpointSecurityData();
  } catch (error) {
    console.error("Error during data migration:", error);
    if (window.NotificationManager) {
      window.NotificationManager.showError(`Error migrating endpoint data: ${error.message}`);
    }
  }
}

// Validate and load security reviews from file data
function validateAndLoadSecurityReviews(reviewsData) {
  const validatedReviews = [];
  const errors = [];

  for (let i = 0; i < reviewsData.length; i++) {
    const reviewData = reviewsData[i];

    try {
      // Validate required fields
      if (!reviewData.endpointId) {
        throw new Error(`Review at index ${i}: Missing endpointId`);
      }

      if (!reviewData.reviewerUsername) {
        throw new Error(`Review at index ${i}: Missing reviewerUsername`);
      }

      if (!reviewData.securityStatus) {
        throw new Error(`Review at index ${i}: Missing securityStatus`);
      }

      // Validate security status
      const validStatuses = getValidSecurityStatuses ? getValidSecurityStatuses() : [
        "Compliant",
        "Non-Compliant", 
        "Risk Accepted",
        "Under Review",
        "Critical Vulnerability",
        "MUST REVIEW"
      ];
      if (!validStatuses.includes(reviewData.securityStatus)) {
        throw new Error(
          `Review at index ${i}: Invalid securityStatus "${
            reviewData.securityStatus
          }". Must be one of: ${validStatuses.join(", ")}`
        );
      }

      // Validate date format
      if (reviewData.reviewDateTime) {
        const reviewDate = new Date(reviewData.reviewDateTime);
        if (isNaN(reviewDate.getTime())) {
          throw new Error(
            `Review at index ${i}: Invalid reviewDateTime format`
          );
        }
        if (reviewDate > new Date()) {
          throw new Error(
            `Review at index ${i}: reviewDateTime cannot be in the future`
          );
        }
      }

      // Validate username format
      if (!/^[a-zA-Z0-9._@-]+$/.test(reviewData.reviewerUsername)) {
        throw new Error(
          `Review at index ${i}: Invalid reviewerUsername format`
        );
      }

      // Validate notes length
      if (reviewData.reviewNotes && reviewData.reviewNotes.length > 5000) {
        throw new Error(
          `Review at index ${i}: reviewNotes exceeds 5000 character limit`
        );
      }

      // Check if endpoint exists
      const endpointExists = AppState.getEndpointData().some((item) => {
        const endpoint =
          item.diffType === "Removed"
            ? item.oldValue
            : item.newValue || item.oldValue;
        return endpoint && endpoint.id === reviewData.endpointId;
      });

      if (!endpointExists) {
        console.warn(
          `Review at index ${i}: Referenced endpoint ${reviewData.endpointId} not found in endpoint data - skipping`
        );
        continue; // Skip this review instead of creating it
      }

      // Create validated review object
      const validatedReview = {
        id: reviewData.id || generateSecurityReviewId(),
        endpointId: reviewData.endpointId,
        reviewDateTime: reviewData.reviewDateTime || getCurrentISOTimestamp(),
        reviewerUsername: reviewData.reviewerUsername,
        reviewNotes: reviewData.reviewNotes || "",
        securityStatus: reviewData.securityStatus,
      };

      validatedReviews.push(validatedReview);
    } catch (error) {
      errors.push(error.message);
      console.error(
        `Validation error for security review at index ${i}:`,
        error.message
      );
    }
  }

  if (errors.length > 0) {
    console.warn(
      `${errors.length} security reviews had validation errors and were skipped`
    );
    if (errors.length === reviewsData.length) {
      throw new Error(
        `All security reviews failed validation: ${errors
          .slice(0, 3)
          .join("; ")}${errors.length > 3 ? "..." : ""}`
      );
    }
  }

  return validatedReviews;
}

// Load security reviews from separate file
function loadSecurityReviewsFromFile() {
  const securityFileName = generateSecurityFileName();
  console.log(`Attempting to load security reviews from: ${securityFileName}`);
  console.log(`Current protocol: ${window.location.protocol}`);

  // Note: Running on file:// protocol - will attempt to load but may fail due to browser security restrictions
  if (window.location.protocol === "file:") {
    console.log(
      "Running in file:// protocol, attempting to load security reviews (may fail due to browser restrictions)"
    );
  }

  return fetch(securityFileName)
    .then((response) => {
      if (!response.ok) {
        if (response.status === 404) {
          console.log(
            `Security file ${securityFileName} not found, initializing empty reviews`
          );
          if (window.NotificationManager) {
            window.NotificationManager.showInfo(
              "No existing security reviews found. Starting with empty review history.",
              {
                persistent: false,
              }
            );
          }
          if (typeof securityReviews !== "undefined") {
            securityReviews.length = 0;
          }
          return null;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then((data) => {
      if (data) {
        try {
          const validatedReviews = validateAndLoadSecurityReviews(
            data.securityReviews || []
          );

          if (typeof securityReviews !== "undefined") {
            securityReviews.length = 0;
            securityReviews.push(...validatedReviews);
            console.log(
              `Successfully loaded ${validatedReviews.length} security reviews from separate file`
            );
            console.log(
              "Sample security review IDs:",
              validatedReviews.slice(0, 3).map((r) => r.endpointId)
            );

            // Clean up any orphaned reviews for non-existent endpoints
            if (typeof cleanupOrphanedReviews === 'function') {
              cleanupOrphanedReviews();
            }

            if (validatedReviews.length > 0) {
              if (window.NotificationManager) {
                window.NotificationManager.showSuccess(
                  `Loaded ${validatedReviews.length} security review${
                    validatedReviews.length > 1 ? "s" : ""
                  } successfully`,
                  {
                    persistent: false,
                  }
                );
              }
            }
          }
        } catch (error) {
          console.error(
            "Error processing security review data from file:",
            error
          );

          // Provide specific error messages based on error type
          let errorMessage = "Failed to process security review data";
          let errorDetails = error.message;

          if (error.name === "SyntaxError") {
            errorMessage = "Security review file contains invalid JSON";
            errorDetails = `JSON parsing error: ${error.message}`;
          } else if (error instanceof ValidationError) {
            errorMessage = "Security review data validation failed";
            errorDetails = error.errors
              ? error.errors.join("\\n")
              : error.message;
          }

          if (window.NotificationManager) {
            window.NotificationManager.showError(errorMessage, {
              details: errorDetails,
              persistent: true,
              allowRetry: true,
              retryCallback: "retryLoadSecurityReviews",
            });
          }

          // Initialize empty reviews on error
          if (typeof securityReviews !== "undefined") {
            securityReviews.length = 0;
          }
        }
      }

      // Update endpoint metadata after loading reviews
      updateEndpointSecurityMetadata();
    })
    .catch((error) => {
      console.error("Error loading security reviews file:", error);

      // Handle different types of network/file errors
      if (
        error.message.includes("404") ||
        error.message.includes("Not Found")
      ) {
        console.log(
          "Security reviews file not found, continuing with empty reviews"
        );
        if (window.NotificationManager) {
          window.NotificationManager.showInfo(
            "No security review file found. You can start adding reviews which will be saved automatically.",
            {
              persistent: false,
            }
          );
        }
      } else if (
        error.message.includes("Failed to fetch") ||
        error.name === "NetworkError"
      ) {
        if (window.location.protocol === "file:") {
          console.log(
            "File protocol detected - security reviews may not load due to browser restrictions"
          );
          if (window.NotificationManager) {
            window.NotificationManager.showWarning(
              "Security reviews cannot be loaded automatically when running from file://. You can still add new reviews.",
              {
                details:
                  "To enable automatic loading, serve the application from a web server (e.g., python -m http.server)",
                persistent: false,
              }
            );
          }
        } else {
          if (window.NotificationManager) {
            window.NotificationManager.showError("Network error loading security reviews", {
              details: error.message,
              persistent: true,
              allowRetry: true,
              retryCallback: "retryLoadSecurityReviews",
            });
          }
        }
      } else {
        if (window.NotificationManager) {
          window.NotificationManager.showError("Unexpected error loading security reviews", {
            details: error.message,
            persistent: true,
            allowRetry: true,
            retryCallback: "retryLoadSecurityReviews",
          });
        }
      }

      // Initialize empty reviews
      if (typeof securityReviews !== "undefined") {
        securityReviews.length = 0;
      }
      updateEndpointSecurityMetadata();
    });
}

// Generate security filename from original filename
function generateSecurityFileName() {
  return FILENAME_ENDPOINTS_SECURITY;
}

// Save security reviews to separate file
function saveSecurityReviewsToFile() {
  if (typeof securityReviews === "undefined" || !securityReviews) {
    console.warn("No security reviews to save");
    return;
  }

  const securityFileName = generateSecurityFileName();
  const securityData = {
    metadata: {
      createdAt: new Date().toISOString(),
      totalReviews: securityReviews.length,
      lastModified: new Date().toISOString(),
    },
    securityReviews: securityReviews,
  };

  // Create downloadable file
  const dataStr = JSON.stringify(securityData, null, 2);
  const dataBlob = new Blob([dataStr], { type: "application/json" });
  const url = URL.createObjectURL(dataBlob);

  const link = document.createElement("a");
  link.href = url;
  link.download = securityFileName.split("/").pop(); // Just the filename without path
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);

  console.log(`Security reviews saved to ${securityFileName}`);
}

// Update endpoint security metadata based on loaded reviews
function updateEndpointSecurityMetadata() {
  console.log("Updating endpoint security metadata...");

  const endpointData = AppState.getEndpointData();
  if (!endpointData || endpointData.length === 0) {
    console.log("No endpoint data to update");
    return;
  }

  let updatedCount = 0;

  // Add security review metadata to each endpoint
  endpointData.forEach((item) => {
    const endpoint =
      item.diffType === "Removed"
        ? item.oldValue
        : item.newValue || item.oldValue;

    if (endpoint && endpoint.id) {
      // Initialize security review metadata
      endpoint.latestSecurityReview = null;
      endpoint.securityReviewCount = 0;

      // Update security review metadata based on existing reviews
      if (
        typeof securityReviews !== "undefined" &&
        securityReviews.length > 0
      ) {
        const endpointReviews = securityReviews.filter(
          (review) => review.endpointId === endpoint.id
        );
        endpoint.securityReviewCount = endpointReviews.length;

        if (endpointReviews.length > 0) {
          // Find the latest review
          const latestReview = endpointReviews.reduce((latest, current) => {
            const latestDate = new Date(latest.reviewDateTime);
            const currentDate = new Date(current.reviewDateTime);
            return currentDate > latestDate ? current : latest;
          });
          endpoint.latestSecurityReview = latestReview;
        }
      }
      updatedCount++;
    }
  });

  console.log(`Updated security review metadata for ${updatedCount} endpoints`);
}

// Migrate existing endpoint data to include security review information
function migrateEndpointSecurityData() {
  console.log("Performing endpoint security data migration...");
  updateEndpointSecurityMetadata();
}

// Helper functions that may be used by security review modules
function generateSecurityReviewId() {
  return 'sr-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}

function getCurrentISOTimestamp() {
  return new Date().toISOString();
}

// Export functions for use by other modules
window.DataLoader = {
  loadDefaultData,
  loadSecurityReviewsFromFile,
  saveSecurityReviewsToFile,
  generateSecurityReviewId,
  getCurrentISOTimestamp
};
