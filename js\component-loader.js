/**
 * Component Loader - Dynamically loads HTML components into the page
 * This allows us to break down large HTML files into smaller, maintainable pieces
 */

class ComponentLoader {
  constructor() {
    this.loadedComponents = new Set();
    this.pendingLoads = new Map();
  }

  /**
   * Load a component from a file and insert it into a target element
   * @param {string} componentPath - Path to the component HTML file
   * @param {string} targetSelector - CSS selector for the target element
   * @param {boolean} append - Whether to append (true) or replace (false) content
   * @param {boolean} insertFirst - Whether to insert at the beginning (true) or append/replace
   * @returns {Promise} Promise that resolves when component is loaded
   */
  async loadComponent(componentPath, targetSelector, append = false, insertFirst = false) {
    // Check if this component is already being loaded
    const loadKey = `${componentPath}-${targetSelector}`;
    if (this.pendingLoads.has(loadKey)) {
      return this.pendingLoads.get(loadKey);
    }

    const loadPromise = this._loadComponentInternal(componentPath, targetSelector, append, insertFirst);
    this.pendingLoads.set(loadKey, loadPromise);

    try {
      const result = await loadPromise;
      this.pendingLoads.delete(loadKey);
      return result;
    } catch (error) {
      this.pendingLoads.delete(loadKey);
      throw error;
    }
  }

  async _loadComponentInternal(componentPath, targetSelector, append, insertFirst = false) {
    try {
      console.log(`Attempting to load component: ${componentPath} -> ${targetSelector}`);
      
      // Check if we're running on file:// protocol
      const isFileProtocol = window.location.protocol === 'file:';
      
      let html;
      if (isFileProtocol) {
        // For file:// protocol, try to use XMLHttpRequest which sometimes works better
        html = await this._loadFileContent(componentPath);
      } else {
        // For http/https, use fetch as normal
        const response = await fetch(componentPath);
        if (!response.ok) {
          throw new Error(`Failed to load component: ${componentPath} (HTTP ${response.status}: ${response.statusText})`);
        }
        html = await response.text();
      }
      
      console.log(`Component ${componentPath} fetched successfully, ${html.length} characters`);
      
      // Find the target element
      const targetElement = document.querySelector(targetSelector);
      if (!targetElement) {
        throw new Error(`Target element not found: ${targetSelector} for component ${componentPath}`);
      }

      // Insert the HTML based on the options
      if (insertFirst) {
        targetElement.insertAdjacentHTML('afterbegin', html);
      } else if (append) {
        targetElement.insertAdjacentHTML('beforeend', html);
      } else {
        targetElement.innerHTML = html;
      }

      // Mark as loaded
      this.loadedComponents.add(componentPath);
      
      console.log(`Component loaded successfully: ${componentPath} -> ${targetSelector} (insertFirst: ${insertFirst}, append: ${append})`);
      return true;
    } catch (error) {
      console.error(`Error loading component ${componentPath}:`, error);
      throw error;
    }
  }

  // Alternative method for loading files using XMLHttpRequest (for file:// protocol)
  _loadFileContent(path) {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      
      // For local files, we might need to handle this differently
      try {
        xhr.open('GET', path, true);
        
        xhr.onload = function() {
          if (xhr.status === 0 || (xhr.status >= 200 && xhr.status < 300)) {
            resolve(xhr.responseText);
          } else {
            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText} for ${path}`));
          }
        };
        
        xhr.onerror = function() {
          reject(new Error(`Network error loading ${path}. Make sure to serve the application via HTTP server.`));
        };
        
        xhr.onabort = function() {
          reject(new Error(`Request aborted for ${path}`));
        };
        
        xhr.send();
      } catch (e) {
        reject(new Error(`Failed to initiate request for ${path}: ${e.message}`));
      }
    });
  }

  /**
   * Load multiple components in parallel
   * @param {Array} components - Array of {path, target, append, insertFirst} objects
   * @returns {Promise} Promise that resolves when all components are loaded
   */
  async loadComponents(components) {
    const promises = components.map(({ path, target, append = false, insertFirst = false }) => 
      this.loadComponent(path, target, append, insertFirst)
    );
    
    return Promise.all(promises);
  }

  /**
   * Check if a component has been loaded
   * @param {string} componentPath - Path to the component
   * @returns {boolean} True if component has been loaded
   */
  isLoaded(componentPath) {
    return this.loadedComponents.has(componentPath);
  }

  /**
   * Get list of loaded components
   * @returns {Array} Array of loaded component paths
   */
  getLoadedComponents() {
    return Array.from(this.loadedComponents);
  }
}

// Create global instance
window.ComponentLoader = new ComponentLoader();

/**
 * Initialize and load all components for the application
 * This function is called after the DOM is ready
 */
async function initializeComponents() {
  try {
    console.log('Loading application components...');

    // Define all components to load
    const components = [
      { path: 'components/left-menu.html', target: '.app-container', append: true, insertFirst: true },
      { path: 'components/top-menu.html', target: '.main-area', append: true, insertFirst: true },
      { path: 'components/main-content.html', target: '.main-area', append: true },
      { path: 'components/detail-view.html', target: '.app-container', append: true },
      { path: 'components/help-modal.html', target: 'body', append: true },
      { path: 'components/security-review-modal.html', target: 'body', append: true },
      { path: 'components/security-review-history-modal.html', target: 'body', append: true },
      { path: 'components/advanced-search-modal.html', target: 'body', append: true }
    ];

    // Load components sequentially to avoid DOM conflicts
    for (const component of components) {
      await window.ComponentLoader.loadComponent(component.path, component.target, component.append, component.insertFirst);
    }

    console.log('All components loaded successfully');
    
    // Dispatch a custom event to notify that components are ready
    document.dispatchEvent(new CustomEvent('componentsLoaded', {
      detail: { loadedComponents: window.ComponentLoader.getLoadedComponents() }
    }));

  } catch (error) {
    console.error('Error loading components:', error);
    
    // Get more detailed error information
    let detailedError = error.message;
    if (error.stack) {
      detailedError += '\n\nStack trace:\n' + error.stack;
    }
    
    // Determine if this is likely a CORS/file protocol issue
    const isFileProtocol = window.location.protocol === 'file:';
    const isCorsError = error.message.includes('Network error') || error.message.includes('Failed to fetch');
    
    let solutionHtml = '';
    if (isFileProtocol || isCorsError) {
      solutionHtml = `
        <div class="alert alert-info mt-3">
          <h5><i class="bi bi-lightbulb me-2"></i>Solution:</h5>
          <p>This error occurs because you're opening the HTML file directly in your browser. To fix this:</p>
          <ol>
            <li><strong>Easy fix:</strong> Run <code>build-standalone.bat</code> to create a standalone version</li>
            <li><strong>Or run a server:</strong> Use <code>start-server.bat</code> (Python) or <code>npm start</code> (Node.js)</li>
          </ol>
          <p>See the README.md file for detailed instructions.</p>
        </div>
      `;
    }
    
    // Show user-friendly error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger m-3';
    errorDiv.innerHTML = `
      <h4>Error Loading Application</h4>
      <p>Failed to load application components. Please refresh the page or contact support if the problem persists.</p>
      ${solutionHtml}
      <details>
        <summary>▼ Technical Details</summary>
        <pre style="white-space: pre-wrap; margin-top: 10px;">${detailedError}</pre>
      </details>
    `;
    document.body.insertBefore(errorDiv, document.body.firstChild);
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ComponentLoader, initializeComponents };
}
