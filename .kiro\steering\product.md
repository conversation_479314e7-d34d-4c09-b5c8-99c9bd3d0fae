# Product Overview

**EndpointInfo Explorer** is a web-based tool for analyzing and exploring API endpoint differences. It provides an interactive interface to view, filter, and compare endpoint changes across different versions of an API.

## Key Features

- Interactive grid view of API endpoints with filtering capabilities
- Detailed comparison view showing old vs new endpoint configurations
- Support for different diff types (Added, Removed, Modified, Unmodified)
- Filtering by HTTP methods, policies, and API client inclusion status
- Resizable panels and customizable view options
- File loading for JSON endpoint data

## Target Use Case

The tool is designed for developers and API maintainers who need to review and understand changes between API versions, particularly useful during API evolution and migration processes.
