// Security Review Integration
// This file serves as the main integration point for all security review functionality

// Initialize all security review components
function initSecurityReviewSystem() {
  console.log("Initializing security review system");
  
  try {
    // Initialize UI components first
    if (typeof initReviewUIComponents === 'function') {
      initReviewUIComponents();
      console.log("✅ Security review UI components initialized");
    } else {
      console.warn("❌ initReviewUIComponents function not found");
    }
    
    // Initialize modal handlers
    if (typeof initSecurityReviewModal === 'function') {
      initSecurityReviewModal();
      console.log("✅ Security review modal initialized");
    } else {
      console.warn("❌ initSecurityReviewModal function not found");
    }
    
    // Initialize history modal
    if (typeof initSecurityReviewHistoryModal === 'function') {
      initSecurityReviewHistoryModal();
      console.log("✅ Security review history modal initialized");
    } else {
      console.warn("❌ initSecurityReviewHistoryModal function not found");
    }
    
    // Initialize advanced search
    if (typeof initAdvancedSearchModal === 'function') {
      initAdvancedSearchModal();
      console.log("✅ Advanced search modal initialized");
    } else {
      console.warn("❌ initAdvancedSearchModal function not found");
    }
    
    // Initialize action buttons AFTER UI components are ready
    // Also call this with a delay to ensure DOM elements exist
    setTimeout(function() {
      if (typeof initSecurityActionButtons === 'function') {
        initSecurityActionButtons();
        console.log("✅ Security action buttons initialized (delayed)");
      } else {
        console.warn("❌ initSecurityActionButtons function not found");
      }
    }, 100);
    
    console.log("Security review system initialized successfully");
  } catch (error) {
    console.error("Error initializing security review system:", error);
  }
}

// Function to refresh the grid after security review changes
function refreshGridAfterSecurityReviewChanges(endpointId) {
  console.log(`Refreshing grid for endpoint ${endpointId}`);
  
  // This function will be overridden by the fixes module
  // Default implementation
  if (typeof window.renderGrid === 'function') {
    window.renderGrid();
  } else if (typeof window.updateGrid === 'function') {
    window.updateGrid();
  }
  
  // Re-initialize button handlers after grid refresh
  setTimeout(function() {
    if (typeof initSecurityActionButtons === 'function') {
      initSecurityActionButtons();
      console.log("🔄 Security action buttons re-initialized after grid refresh");
    }
  }, 50);
}

// Function to manually re-initialize security button handlers
function reinitializeSecurityButtons() {
  console.log("🔄 Manually re-initializing security button handlers");
  if (typeof initSecurityActionButtons === 'function') {
    initSecurityActionButtons();
    console.log("✅ Security action buttons re-initialized");
  } else {
    console.warn("❌ initSecurityActionButtons function not found");
  }
}

// Show success message
function showSuccessMessage(message) {
  console.log("Success message:", message);
  
  // This function will be overridden by the fixes module
  // Default implementation using alert
  alert(message);
}

// Export core functions for backward compatibility
if (typeof window !== "undefined") {
  // Make sure these functions are available globally
  window.initSecurityReviewSystem = initSecurityReviewSystem;
  window.refreshGridAfterSecurityReviewChanges = refreshGridAfterSecurityReviewChanges;
  window.reinitializeSecurityButtons = reinitializeSecurityButtons;
  window.showSuccessMessage = showSuccessMessage;
  
  // Legacy compatibility - these will be overridden by the fixes module
  window.initSecurityReviewHistory = function() {
    console.log("Legacy initSecurityReviewHistory called");
  };
  
  // Manual test function for debugging
  window.testSecurityButtons = function() {
    console.log("Testing security buttons...");
    console.log("Note: Button handling is now in review-fixes.js");
    
    // Check if functions are available
    const functions = [
      'showSecurityReviewModal',
      'showSecurityReviewHistoryModal',
      'refreshGridAfterSecurityReviewChanges',
      'showDetailView'
    ];
    
    functions.forEach(funcName => {
      const available = typeof window[funcName] === 'function';
      console.log(`${funcName}: ${available ? '✅ Available' : '❌ Missing'}`);
    });
    
    // Check current endpoint
    const currentEndpoint = window.AppState ? window.AppState.getCurrentEndpoint() : window.currentEndpoint;
    console.log(`Current endpoint: ${currentEndpoint ? currentEndpoint.id : 'none'}`);
    
    // Find and test button clicks
    const addBtns = document.querySelectorAll('.add-review-btn');
    const historyBtns = document.querySelectorAll('.view-history-btn');
    
    console.log(`Found ${addBtns.length} add review buttons`);
    console.log(`Found ${historyBtns.length} view history buttons`);
    
    if (addBtns.length > 0) {
      console.log("First add button attributes:");
      const btn = addBtns[0];
      console.log(`  data-endpoint-id: ${btn.getAttribute('data-endpoint-id')}`);
      console.log(`  data-http-method: ${btn.getAttribute('data-http-method')}`);
      console.log(`  data-route: ${btn.getAttribute('data-route')}`);
      console.log("💡 Click the button manually to test");
    }
    
    if (historyBtns.length > 0) {
      console.log("First history button attributes:");
      const btn = historyBtns[0];
      console.log(`  data-endpoint-id: ${btn.getAttribute('data-endpoint-id')}`);
      console.log(`  data-http-method: ${btn.getAttribute('data-http-method')}`);
      console.log(`  data-route: ${btn.getAttribute('data-route')}`);
      console.log("💡 Click the button manually to test");
    }
  };
  
  // Manual refresh detail view function
  window.refreshCurrentDetailView = function() {
    const currentEndpoint = window.AppState ? window.AppState.getCurrentEndpoint() : window.currentEndpoint;
    if (currentEndpoint) {
      console.log(`Manually refreshing detail view for endpoint: ${currentEndpoint.id}`);
      if (typeof window.showDetailView === 'function') {
        window.showDetailView(currentEndpoint);
        console.log("✅ Detail view refreshed");
      } else {
        console.log("❌ showDetailView function not available");
      }
    } else {
      console.log("❌ No current endpoint to refresh");
    }
  };
}

// Auto-initialize when DOM is ready
document.addEventListener("DOMContentLoaded", function() {
  // Wait for components to be loaded before initializing
  if (typeof window.ComponentLoader !== 'undefined' && window.ComponentLoader.getLoadedComponents) {
    // Check if components are already loaded
    const loadedComponents = window.ComponentLoader.getLoadedComponents();
    if (loadedComponents.length > 0) {
      console.log("Components already loaded, initializing security review system immediately");
      setTimeout(initSecurityReviewSystem, 100);
    } else {
      console.log("Waiting for components to load...");
      document.addEventListener('componentsLoaded', function() {
        console.log("Components loaded event received, initializing security review system");
        setTimeout(initSecurityReviewSystem, 100);
      });
    }
  } else {
    console.log("ComponentLoader not available, initializing security review system with delay");
    // Fallback: wait a bit longer for everything to load
    setTimeout(initSecurityReviewSystem, 500);
  }
});
