<!-- Left Menu -->
<div class="left-menu" id="left-menu">
  <div class="left-menu-resizer" id="left-menu-resizer"></div>
  <div class="filter-section">
    <h3>Filters</h3>
    <div class="filter-group">
      <label for="search-input">Search:</label>
      <input
        type="text"
        id="search-input"
        class="filter-input form-control"
        placeholder="Search endpoints..."
      />
    </div>

    <div class="filter-group">
      <h3>Diff Type</h3>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-added"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-added">Added</label>
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-deleted"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-deleted"
          >Removed</label
        >
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-modified"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-modified"
          >Modified</label
        >
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-unmodified"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-unmodified"
          >Unmodified</label
        >
      </div>
    </div>

    <div class="filter-group">
      <h3>HTTP Verb</h3>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-get"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-get">GET</label>
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-post"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-post">POST</label>
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-put"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-put">PUT</label>
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-delete"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-delete">DELETE</label>
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-patch"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-patch">PATCH</label>
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-blank"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-blank">Blank</label>
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-other"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-other">Other</label>
      </div>
    </div>

    <div class="filter-group">
      <h3>Policy</h3>
      <div class="radio-group form-check">
        <input
          type="radio"
          id="policy-all"
          name="policy-filter"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="policy-all">ALL</label>
      </div>
      <div class="radio-group form-check">
        <input
          type="radio"
          id="policy-allow-anonymous"
          name="policy-filter"
          class="form-check-input"
        />
        <label class="form-check-label" for="policy-allow-anonymous"
          >AllowAnonymous</label
        >
      </div>
      <div class="radio-group form-check">
        <input
          type="radio"
          id="policy-default"
          name="policy-filter"
          class="form-check-input"
        />
        <label class="form-check-label" for="policy-default"
          >Default</label
        >
      </div>
      <div class="radio-group form-check">
        <input
          type="radio"
          id="policy-blank"
          name="policy-filter"
          class="form-check-input"
        />
        <label class="form-check-label" for="policy-blank">Blank</label>
      </div>
    </div>

    <div class="filter-group">
      <h3>Include in Api Client</h3>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-api-client-included"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-api-client-included"
          >Included</label
        >
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-api-client-excluded"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-api-client-excluded"
          >Excluded</label
        >
      </div>
    </div>

    <div class="filter-group">
      <h3>Security Status</h3>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-security-compliant"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-security-compliant"
          >Compliant</label
        >
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-security-non-compliant"
          class="form-check-input"
          checked
        />
        <label
          class="form-check-label"
          for="filter-security-non-compliant"
          >Non-Compliant</label
        >
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-security-risk-accepted"
          class="form-check-input"
          checked
        />
        <label
          class="form-check-label"
          for="filter-security-risk-accepted"
          >Risk Accepted</label
        >
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-security-under-review"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-security-under-review"
          >Under Review</label
        >
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-security-critical-vulnerability"
          class="form-check-input"
          checked
        />
        <label
          class="form-check-label"
          for="filter-security-critical-vulnerability"
          >Critical Vulnerability</label
        >
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-security-must-review"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-security-must-review"
          >MUST REVIEW</label
        >
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-security-none"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-security-none"
          >No Review</label
        >
      </div>
    </div>

    <div class="filter-group">
      <h3>Security Reviews</h3>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-has-reviews"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-has-reviews"
          >Has Reviews</label
        >
      </div>
      <div class="checkbox-group form-check">
        <input
          type="checkbox"
          id="filter-no-reviews"
          class="form-check-input"
          checked
        />
        <label class="form-check-label" for="filter-no-reviews"
          >No Reviews</label
        >
      </div>
    </div>
  </div>
</div>
