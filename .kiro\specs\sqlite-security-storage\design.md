# Design Document

## Overview

This design implements SQLite database storage for security reviews in the EndpointInfo Explorer application. The system will maintain backward compatibility with the existing JSON-based approach while providing more robust data persistence through SQLite databases. The implementation will be client-side focused, using browser-compatible SQLite solutions.

## Architecture

### Current System Analysis

The application currently:

- Loads endpoint data from JSON files (e.g., `data/endpoints.json`)
- Stores security reviews in corresponding JSON files (e.g., `data/endpoints-security.json`)
- Uses a global `securityReviews` array for in-memory management
- Provides CRUD operations through JavaScript functions in `security-review.js`

### Proposed Architecture

The new system will:

- Maintain the same file naming convention but with `.sqlite3` extension
- Use SQL.js (SQLite compiled to WebAssembly) for browser compatibility
- Implement a database abstraction layer to handle SQLite operations
- Preserve existing API interfaces for seamless integration
- Support both read and write operations to SQLite databases

## Components and Interfaces

### 1. Database Manager (`SecurityDatabaseManager`)

**Purpose**: Central component for managing SQLite database operations

**Key Methods**:

```javascript
class SecurityDatabaseManager {
  constructor(databasePath)
  async initialize()
  async createDatabase()
  async loadDatabase()
  async saveDatabase()
  async executeQuery(sql, params)
  async close()
}
```

**Responsibilities**:

- Database file management (create, load, save)
- SQL query execution
- Connection lifecycle management
- Error handling and recovery

### 2. Security Review Repository (`SecurityReviewRepository`)

**Purpose**: Data access layer for security review operations

**Key Methods**:

```javascript
class SecurityReviewRepository {
  constructor(databaseManager)
  async createSecurityReview(reviewData)
  async getSecurityReviewsForEndpoint(endpointId)
  async getLatestSecurityReview(endpointId)
  async getAllSecurityReviews()
  async updateSecurityReview(reviewId, updateData)
  async deleteSecurityReview(reviewId)
  async getSecurityReviewStats()
}
```

**Responsibilities**:

- CRUD operations for security reviews
- Data validation and transformation
- Query optimization
- Statistics and reporting

### 3. Database Schema Manager (`SchemaManager`)

**Purpose**: Handles database schema creation and migrations

**Key Methods**:

```javascript
class SchemaManager {
  constructor(databaseManager)
  async createSchema()
  async validateSchema()
  getSchemaVersion()
}
```

**Responsibilities**:

- Initial schema creation
- Schema validation
- Version management (for future migrations)

### 4. Storage Adapter (`SecurityStorageAdapter`)

**Purpose**: Abstraction layer that maintains compatibility with existing code

**Key Methods**:

```javascript
class SecurityStorageAdapter {
  constructor(databasePath)
  async initialize()
  async loadSecurityReviews()
  async saveSecurityReview(reviewData)
  async getSecurityReviewsForEndpoint(endpointId)
  // ... other methods matching existing API
}
```

**Responsibilities**:

- API compatibility with existing functions
- Automatic database path resolution
- Fallback handling for missing databases

## Data Models

### Database Schema

**Table: security_reviews**:

```sql
CREATE TABLE security_reviews (
    id TEXT PRIMARY KEY,
    endpoint_id TEXT NOT NULL,
    review_date_time TEXT NOT NULL,
    reviewer_username TEXT NOT NULL,
    review_notes TEXT,
    security_status TEXT NOT NULL,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_security_status CHECK (security_status IN (
        'Compliant', 
        'Non-Compliant', 
        'Risk Accepted', 
        'Under Review', 
        'Critical Vulnerability'
    ))
);

CREATE INDEX idx_security_reviews_endpoint_id ON security_reviews(endpoint_id);
CREATE INDEX idx_security_reviews_date ON security_reviews(review_date_time);
CREATE INDEX idx_security_reviews_status ON security_reviews(security_status);
CREATE INDEX idx_security_reviews_reviewer ON security_reviews(reviewer_username);
```

**Table: metadata**:

```sql
CREATE TABLE metadata (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### Data Mapping

**JSON to SQLite Mapping**:

- `id` → `id` (TEXT PRIMARY KEY)
- `endpointId` → `endpoint_id` (TEXT NOT NULL)
- `securityStatus` → `security_status` (TEXT NOT NULL)
- `reviewDateTime` → `review_date_time` (TIMESTAMP NOT NULL)
- `reviewerUsername` → `reviewer_username` (TEXT NOT NULL)
- `reviewNotes` → `review_notes` (TEXT)

## Error Handling

### Database Connection Errors

- **File not found**: Create new database with schema
- **Corruption**: Log error, attempt recovery, fallback to new database
- **Permission denied**: Display user-friendly error message
- **Browser compatibility**: Detect SQL.js support, provide fallback message

### Data Validation Errors

- **Invalid data types**: Validate before database operations
- **Constraint violations**: Provide specific error messages
- **Missing required fields**: Client-side validation with clear feedback

### Recovery Strategies

- **Automatic database creation**: When database file doesn't exist
- **Schema validation**: Ensure correct table structure on load
- **Graceful degradation**: Continue operation with in-memory storage if database fails
- **Error logging**: Comprehensive logging for debugging

## Testing Strategy

### Unit Tests

- **Database Manager**: Connection, query execution, error handling
- **Repository Layer**: CRUD operations, data validation
- **Schema Manager**: Table creation, constraint validation
- **Storage Adapter**: API compatibility, data transformation

### Integration Tests

- **End-to-end workflows**: Create review → Save to database → Retrieve → Display
- **File operations**: Database creation, loading, saving
- **Error scenarios**: Missing files, corrupted databases, invalid data

### Browser Compatibility Tests

- **SQL.js loading**: Verify WebAssembly support
- **File system operations**: Test in different browser environments
- **Performance**: Large dataset handling, query optimization

### Data Migration Tests

- **Schema validation**: Ensure correct table structure
- **Data integrity**: Verify all fields are properly mapped
- **Backward compatibility**: Existing functionality continues to work

## Implementation Phases

### Phase 1: Core Database Infrastructure

- Implement `SecurityDatabaseManager`
- Set up SQL.js integration
- Create basic schema management
- Add error handling framework

### Phase 2: Repository Layer

- Implement `SecurityReviewRepository`
- Add CRUD operations
- Implement data validation
- Create query optimization

### Phase 3: Storage Adapter

- Implement `SecurityStorageAdapter`
- Ensure API compatibility
- Add automatic database path resolution
- Implement fallback mechanisms

### Phase 4: Integration and Testing

- Integrate with existing security review system
- Replace global array with database operations
- Add comprehensive error handling
- Implement browser compatibility checks

## Dependencies

### Required Libraries

- **SQL.js**: SQLite compiled to WebAssembly for browser compatibility
- **File System Access API**: For reading/writing database files (with fallbacks)

### Browser Requirements

- **WebAssembly support**: Required for SQL.js
- **Modern JavaScript**: ES6+ features (async/await, classes)
- **File handling**: FileReader API for database file operations

## Performance Considerations

### Query Optimization

- **Indexes**: On frequently queried columns (endpointId, reviewDateTime)
- **Prepared statements**: For repeated queries
- **Batch operations**: For multiple inserts/updates

### Memory Management

- **Connection pooling**: Reuse database connections
- **Result set limits**: Pagination for large datasets
- **Cleanup**: Proper resource disposal

### File Operations

- **Lazy loading**: Load database only when needed
- **Caching**: Keep frequently accessed data in memory
- **Compression**: Consider database file compression for storage efficiency
