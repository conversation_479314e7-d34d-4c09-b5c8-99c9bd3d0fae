// Security Review Data Models and Validation
// This file contains data structures, enums, and validation logic

// Security Status Enumeration
const SECURITY_STATUS = {
  COMPLIANT: "Compliant",
  NON_COMPLIANT: "Non-Compliant",
  RISK_ACCEPTED: "Risk Accepted",
  UNDER_REVIEW: "Under Review",
  CRITICAL_VULNERABILITY: "Critical Vulnerability",
  MUST_REVIEW: "MUST REVIEW",
};

// Custom validation error class
class ValidationError extends Error {
  constructor(message, errors = []) {
    super(message);
    this.name = "ValidationError";
    this.errors = errors;
  }
}

// EndpointSecurityReview Data Model
class EndpointSecurityReview {
  constructor(data) {
    this.id = data.id || generateSecurityReviewId();
    this.endpointId = data.endpointId;
    this.reviewDateTime = data.reviewDateTime || getCurrentISOTimestamp();
    this.reviewerUsername = data.reviewerUsername;
    this.securityStatus = data.securityStatus;
    this.reviewNotes = data.reviewNotes || "";

    // Validate the review data
    this.validate();
  }

  validate() {
    const errors = [];

    // Validate required fields
    if (!this.endpointId) {
      errors.push("Endpoint ID is required");
    }

    if (!this.reviewerUsername || this.reviewerUsername.trim() === "") {
      errors.push("Reviewer username is required");
    }

    if (!this.securityStatus) {
      errors.push("Security status is required");
    }

    // Validate security status value
    if (this.securityStatus && !isValidSecurityStatus(this.securityStatus)) {
      errors.push(`Invalid security status: ${this.securityStatus}. Valid values are: ${getValidSecurityStatuses().join(", ")}`);
    }

    // Validate review date
    if (this.reviewDateTime) {
      const reviewDate = new Date(this.reviewDateTime);
      if (isNaN(reviewDate.getTime())) {
        errors.push("Invalid review date/time format");
      }
    }

    // Validate reviewer username format
    if (this.reviewerUsername) {
      const usernamePattern = /^[a-zA-Z0-9._@-]+$/;
      if (!usernamePattern.test(this.reviewerUsername)) {
        errors.push("Reviewer username contains invalid characters. Only letters, numbers, dots, underscores, @ symbols, and hyphens are allowed");
      }
    }

    // Validate notes length (optional but reasonable limit)
    if (this.reviewNotes && this.reviewNotes.length > 5000) {
      errors.push("Review notes must be less than 5000 characters");
    }

    if (errors.length > 0) {
      throw new ValidationError("Validation failed", errors);
    }

    return true;
  }

  // Convert to plain object for serialization
  toJSON() {
    return {
      id: this.id,
      endpointId: this.endpointId,
      reviewDateTime: this.reviewDateTime,
      reviewerUsername: this.reviewerUsername,
      securityStatus: this.securityStatus,
      reviewNotes: this.reviewNotes
    };
  }
}

// Validation utility functions
function getValidSecurityStatuses() {
  return Object.values(SECURITY_STATUS);
}

function isValidSecurityStatus(status) {
  return getValidSecurityStatuses().includes(status);
}

function validateEndpointExists(endpointId) {
  if (!window.endpointData || window.endpointData.length === 0) {
    return false;
  }

  return window.endpointData.some((item) => {
    const endpoint = item.diffType === "Removed" ? item.oldValue : item.newValue || item.oldValue;
    return endpoint && endpoint.id === endpointId;
  });
}

// Export for browser environment
if (typeof window !== "undefined") {
  window.SECURITY_STATUS = SECURITY_STATUS;
  window.ValidationError = ValidationError;
  window.EndpointSecurityReview = EndpointSecurityReview;
  window.getValidSecurityStatuses = getValidSecurityStatuses;
  window.isValidSecurityStatus = isValidSecurityStatus;
  window.validateEndpointExists = validateEndpointExists;
}

// Export for module environment
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    SECURITY_STATUS,
    ValidationError,
    EndpointSecurityReview,
    getValidSecurityStatuses,
    isValidSecurityStatus,
    validateEndpointExists
  };
}
