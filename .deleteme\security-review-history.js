// Security Review History Functionality
// This file implements the security review history display functionality

// Initialize security review history functionality
function initSecurityReviewHistory() {
  console.log("Initializing security review history functionality");
  
  // Add event listeners for the history modal
  document.addEventListener("DOMContentLoaded", function() {
    // Initialize sort buttons
    initSortButtons();
    
    // Initialize expand/collapse functionality for review notes
    initExpandCollapseNotes();
  });
}

// Show security review history modal for an endpoint with enhanced error handling
function showSecurityReviewHistoryModal(endpoint) {
  console.log("showSecurityReviewHistoryModal called with:", endpoint);
  
  try {
    if (!endpoint) {
      console.error("Cannot show security review history modal: No endpoint provided");
      showError("Cannot display review history: No endpoint selected", {
        persistent: false,
        showInGrid: false
      });
      return;
    }

    // Get the modal element
    const modal = document.getElementById("securityReviewHistoryModal");
    if (!modal) {
      console.error("Security review history modal not found in DOM");
      showError("Review history interface not available", {
        details: "The security review history modal is missing from the page. Please refresh and try again.",
        persistent: true,
        showInGrid: false
      });
      return;
    }

    // Check if Bootstrap is available
    if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
      console.error("Bootstrap Modal is not available");
      showError("Modal system not available", {
        details: "Bootstrap Modal library is not loaded. Please refresh the page and try again.",
        persistent: true,
        showInGrid: false
      });
      return;
    }

    // Get the Bootstrap modal instance
    const modalInstance = bootstrap.Modal.getOrCreateInstance(modal);

    // Extract endpoint information with fallbacks
    const httpMethod = endpoint.httpMethod || 
      (endpoint.httpMethods && Array.isArray(endpoint.httpMethods) ? endpoint.httpMethods.join(", ") : null) ||
      (endpoint.newValue && endpoint.newValue.httpMethod) || 
      (endpoint.newValue && endpoint.newValue.httpMethods && Array.isArray(endpoint.newValue.httpMethods) ? endpoint.newValue.httpMethods.join(", ") : null) ||
      (endpoint.oldValue && endpoint.oldValue.httpMethod) || 
      (endpoint.oldValue && endpoint.oldValue.httpMethods && Array.isArray(endpoint.oldValue.httpMethods) ? endpoint.oldValue.httpMethods.join(", ") : null) ||
      "Unknown";
      
    const route = endpoint.route || 
      (endpoint.newValue && endpoint.newValue.route) || 
      (endpoint.oldValue && endpoint.oldValue.route) || 
      "Unknown Route";

    // Set endpoint information in the modal
    const httpMethodElement = document.getElementById("history-http-method");
    const routeElement = document.getElementById("history-route");
    
    if (httpMethodElement) {
      httpMethodElement.textContent = httpMethod;
    }
    
    if (routeElement) {
      routeElement.textContent = route;
      routeElement.title = route; // Add tooltip for long routes
    }
    
    // Load and display reviews for this endpoint
    loadReviewsForEndpoint(endpoint.id);
    
    // Show the modal
    modalInstance.show();
    
  } catch (error) {
    console.error("Error showing security review history modal:", error);
    showError("Failed to open review history", {
      details: error.message,
      persistent: true,
      showInGrid: false,
      allowRetry: true,
      retryCallback: 'retryShowHistoryModal'
    });
  }
}

// Load and display all reviews for a specific endpoint with enhanced error handling
function loadReviewsForEndpoint(endpointId) {
  try {
    if (!endpointId) {
      throw new Error("No endpoint ID provided");
    }

    console.log(`Loading reviews for endpoint: ${endpointId}`);
    
    // Show loading state
    const tbody = document.getElementById("review-history-tbody");
    const noReviewsMessage = document.getElementById("no-reviews-message");
    const reviewHistoryContainer = document.getElementById("review-history-container");
    
    if (!tbody) {
      throw new Error("Review history table not found in the interface");
    }
    
    // Show loading indicator
    tbody.innerHTML = `
      <tr>
        <td colspan="4" class="text-center p-4">
          <div class="d-flex align-items-center justify-content-center">
            <div class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></div>
            <span>Loading review history...</span>
          </div>
        </td>
      </tr>`;
    
    // Hide messages while loading
    if (noReviewsMessage) noReviewsMessage.classList.add("d-none");
    if (reviewHistoryContainer) reviewHistoryContainer.classList.remove("d-none");
    
    // Get all reviews for this endpoint
    let reviews;
    try {
      reviews = getSecurityReviewsForEndpoint(endpointId);
    } catch (error) {
      throw new Error(`Failed to retrieve reviews: ${error.message}`);
    }
    
    // Clear loading state
    tbody.innerHTML = "";
    
    // Show/hide elements based on whether there are reviews
    if (!reviews || reviews.length === 0) {
      console.log(`No reviews found for endpoint: ${endpointId}`);
      
      if (noReviewsMessage) {
        noReviewsMessage.classList.remove("d-none");
        // Enhance the no reviews message
        noReviewsMessage.innerHTML = `
          <div class="d-flex align-items-center">
            <i class="bi bi-info-circle me-2"></i>
            <div>
              <strong>No security reviews found</strong>
              <div class="small text-muted">This endpoint has not been reviewed yet. You can add the first review using the "Add Review" button in the main grid.</div>
            </div>
          </div>`;
      }
      
      if (reviewHistoryContainer) reviewHistoryContainer.classList.add("d-none");
      return;
    } else {
      console.log(`Found ${reviews.length} review${reviews.length > 1 ? 's' : ''} for endpoint: ${endpointId}`);
      
      if (noReviewsMessage) noReviewsMessage.classList.add("d-none");
      if (reviewHistoryContainer) reviewHistoryContainer.classList.remove("d-none");
    }
    
    // Create rows for each review with error handling
    const validRows = [];
    const errorRows = [];
    
    reviews.forEach((review, index) => {
      try {
        const row = createReviewHistoryRow(review, index);
        validRows.push(row);
      } catch (error) {
        console.error(`Error creating row for review ${index}:`, error);
        errorRows.push({ index, error: error.message });
      }
    });
    
    // Add valid rows to table
    validRows.forEach(row => {
      tbody.appendChild(row);
    });
    
    // Show warning if some rows failed to create
    if (errorRows.length > 0) {
      const errorRow = document.createElement("tr");
      errorRow.className = "table-warning";
      errorRow.innerHTML = `
        <td colspan="4" class="text-center p-2">
          <small class="text-warning">
            <i class="bi bi-exclamation-triangle me-1"></i>
            ${errorRows.length} review${errorRows.length > 1 ? 's' : ''} could not be displayed due to data issues
          </small>
        </td>`;
      tbody.appendChild(errorRow);
    }
    
    // Set the initial sort (newest first) with error handling
    try {
      sortReviewHistoryTable("date", "desc");
      updateSortButtonStates("date", "desc");
    } catch (error) {
      console.error("Error sorting review history table:", error);
      showWarning("Review history loaded but sorting failed", {
        details: error.message,
        persistent: false
      });
    }
    
  } catch (error) {
    console.error("Error loading reviews for endpoint:", error);
    
    // Show error in the table
    const tbody = document.getElementById("review-history-tbody");
    if (tbody) {
      tbody.innerHTML = `
        <tr>
          <td colspan="4" class="text-center text-danger p-4">
            <div class="alert alert-danger mb-0" role="alert">
              <div class="d-flex align-items-center justify-content-center mb-2">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <strong>Error Loading Reviews</strong>
              </div>
              <p class="mb-2">${escapeHtml(error.message)}</p>
              <button class="btn btn-sm btn-outline-danger" onclick="retryLoadReviews('${endpointId}')">
                <i class="bi bi-arrow-clockwise me-1"></i>Retry
              </button>
            </div>
          </td>
        </tr>`;
    }
    
    // Also show toast notification
    showError("Failed to load review history", {
      details: error.message,
      persistent: false,
      showInGrid: false
    });
  }
}

// Create a table row for a security review
function createReviewHistoryRow(review, index) {
  const row = document.createElement("tr");
  
  // Format the date
  const formattedDate = formatReviewDate(review.reviewDateTime);
  
  // Store the timestamp as a data attribute for sorting
  const timestamp = new Date(review.reviewDateTime).getTime();
  
  // Create the date cell
  const dateCell = document.createElement("td");
  dateCell.setAttribute("data-sort-value", timestamp);
  dateCell.textContent = formattedDate;
  
  // Create the reviewer cell
  const reviewerCell = document.createElement("td");
  reviewerCell.textContent = review.reviewerUsername;
  
  // Create the status cell with appropriate styling
  const statusCell = document.createElement("td");
  const statusClass = getSecurityStatusClass(review.securityStatus);
  statusCell.innerHTML = `<span class="security-status ${statusClass}">${review.securityStatus}</span>`;
  
  // Create the notes cell with expand/collapse functionality
  const notesCell = document.createElement("td");
  
  // Check if notes are empty
  if (!review.reviewNotes || review.reviewNotes.trim() === "") {
    notesCell.innerHTML = '<em class="text-muted">No notes provided</em>';
  } else {
    // Create a container for the notes
    const notesContainer = document.createElement("div");
    notesContainer.className = "review-notes-container";
    
    // Create a preview of the notes (first 100 characters)
    const notesPreview = document.createElement("div");
    notesPreview.className = "review-notes-preview";
    const previewText = review.reviewNotes.length > 100 
      ? review.reviewNotes.substring(0, 100) + "..." 
      : review.reviewNotes;
    notesPreview.textContent = previewText;
    
    // Create the full notes content (initially hidden)
    const notesContent = document.createElement("div");
    notesContent.className = "review-notes-content d-none";
    
    // Convert markdown to HTML if a markdown parser is available
    if (typeof marked === 'function') {
      notesContent.innerHTML = marked(review.reviewNotes);
    } else {
      // Simple formatting for line breaks if no markdown parser
      notesContent.innerHTML = review.reviewNotes.replace(/\n/g, "<br>");
    }
    
    // Create expand/collapse toggle button
    const toggleButton = document.createElement("button");
    toggleButton.className = "btn btn-sm btn-link review-notes-toggle";
    toggleButton.textContent = "Show more";
    toggleButton.setAttribute("data-expanded", "false");
    toggleButton.setAttribute("data-review-index", index);
    
    // Add elements to the notes cell
    notesContainer.appendChild(notesPreview);
    notesContainer.appendChild(notesContent);
    notesContainer.appendChild(toggleButton);
    notesCell.appendChild(notesContainer);
  }
  
  // Add cells to the row
  row.appendChild(dateCell);
  row.appendChild(reviewerCell);
  row.appendChild(statusCell);
  row.appendChild(notesCell);
  
  return row;
}

// Initialize expand/collapse functionality for review notes
function initExpandCollapseNotes() {
  // Use event delegation for toggle buttons
  document.addEventListener("click", function(e) {
    const toggleButton = e.target.closest(".review-notes-toggle");
    if (!toggleButton) return;
    
    // Get the review index
    const reviewIndex = toggleButton.getAttribute("data-review-index");
    
    // Get the notes container
    const notesContainer = toggleButton.closest(".review-notes-container");
    if (!notesContainer) return;
    
    // Get the preview and content elements
    const preview = notesContainer.querySelector(".review-notes-preview");
    const content = notesContainer.querySelector(".review-notes-content");
    
    // Check if currently expanded
    const isExpanded = toggleButton.getAttribute("data-expanded") === "true";
    
    if (isExpanded) {
      // Collapse
      if (preview) preview.classList.remove("d-none");
      if (content) content.classList.add("d-none");
      toggleButton.textContent = "Show more";
      toggleButton.setAttribute("data-expanded", "false");
    } else {
      // Expand
      if (preview) preview.classList.add("d-none");
      if (content) content.classList.remove("d-none");
      toggleButton.textContent = "Show less";
      toggleButton.setAttribute("data-expanded", "true");
    }
  });
}

// Initialize sort buttons
function initSortButtons() {
  // Use event delegation for sort buttons
  document.addEventListener("click", function(e) {
    const sortBtn = e.target.closest(".sort-btn");
    if (!sortBtn) return;
    
    const sortBy = sortBtn.getAttribute("data-sort-by");
    let sortOrder = sortBtn.getAttribute("data-sort-order") || "asc";
    
    // Toggle sort order if clicking the same button again
    if (sortBtn.classList.contains("active")) {
      sortOrder = sortOrder === "asc" ? "desc" : "asc";
    }
    
    // Sort the table
    sortReviewHistoryTable(sortBy, sortOrder);
    
    // Update sort button states
    updateSortButtonStates(sortBy, sortOrder);
  });
  
  // Also add click handlers for table headers
  document.addEventListener("click", function(e) {
    const headerCell = e.target.closest("#review-history-table th");
    if (!headerCell) return;
    
    // Determine which column was clicked
    const headerIndex = Array.from(headerCell.parentNode.children).indexOf(headerCell);
    let sortBy;
    
    // Map header index to sort column
    switch (headerIndex) {
      case 0: // Date/Time column
        sortBy = "date";
        break;
      case 1: // Reviewer column
        sortBy = "reviewer";
        break;
      case 2: // Status column
        sortBy = "status";
        break;
      default:
        return; // Don't sort by other columns
    }
    
    // Find the corresponding sort button
    const sortBtn = document.querySelector(`.sort-btn[data-sort-by="${sortBy}"]`);
    if (!sortBtn) return;
    
    // Get current sort order
    let sortOrder = sortBtn.getAttribute("data-sort-order") || "asc";
    
    // Toggle sort order if this column is already active
    if (sortBtn.classList.contains("active")) {
      sortOrder = sortOrder === "asc" ? "desc" : "asc";
    }
    
    // Sort the table
    sortReviewHistoryTable(sortBy, sortOrder);
    
    // Update sort button states
    updateSortButtonStates(sortBy, sortOrder);
  });
}

// Update sort button states
function updateSortButtonStates(activeSortBy, activeSortOrder) {
  const sortButtons = document.querySelectorAll(".sort-btn");
  
  sortButtons.forEach(button => {
    const sortBy = button.getAttribute("data-sort-by");
    
    // Reset all buttons
    button.classList.remove("active");
    button.textContent = button.textContent.replace(" ↑", "").replace(" ↓", "");
    
    // Update the active button
    if (sortBy === activeSortBy) {
      button.classList.add("active");
      button.setAttribute("data-sort-order", activeSortOrder);
      button.textContent = button.textContent + (activeSortOrder === "asc" ? " ↑" : " ↓");
    }
  });
}

// Sort the review history table
function sortReviewHistoryTable(sortBy, sortOrder = "asc") {
  console.log(`Sorting review history table by ${sortBy} in ${sortOrder} order`);
  
  const tbody = document.getElementById("review-history-tbody");
  if (!tbody) {
    console.warn("Review history table body not found");
    return;
  }
  
  // Get all rows as an array for sorting
  const rows = Array.from(tbody.querySelectorAll("tr"));
  if (rows.length === 0) {
    console.warn("No rows found in review history table");
    return;
  }
  
  // Define sort functions for different columns
  const sortFunctions = {
    date: (a, b) => {
      const aValue = parseInt(a.querySelector("td:first-child").getAttribute("data-sort-value") || "0", 10);
      const bValue = parseInt(b.querySelector("td:first-child").getAttribute("data-sort-value") || "0", 10);
      return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
    },
    reviewer: (a, b) => {
      const aValue = a.querySelector("td:nth-child(2)").textContent.toLowerCase();
      const bValue = b.querySelector("td:nth-child(2)").textContent.toLowerCase();
      return sortOrder === "asc" 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    },
    status: (a, b) => {
      const aValue = a.querySelector("td:nth-child(3)").textContent.toLowerCase();
      const bValue = b.querySelector("td:nth-child(3)").textContent.toLowerCase();
      return sortOrder === "asc" 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    }
  };
  
  // Sort the rows
  rows.sort(sortFunctions[sortBy] || sortFunctions.date);
  
  // Remove all rows from the table
  while (tbody.firstChild) {
    tbody.removeChild(tbody.firstChild);
  }
  
  // Add the sorted rows back to the table
  rows.forEach(row => {
    tbody.appendChild(row);
  });
}

// Retry functions for error recovery
window.retryLoadReviews = function(endpointId) {
  loadReviewsForEndpoint(endpointId);
};

window.retryShowHistoryModal = function() {
  if (window.currentEndpoint) {
    showSecurityReviewHistoryModal(window.currentEndpoint);
  } else {
    showWarning("Cannot retry: No endpoint selected", {
      persistent: false
    });
  }
};

// Add the initialization function to the window object
window.initSecurityReviewHistory = initSecurityReviewHistory;
window.showSecurityReviewHistoryModal = showSecurityReviewHistoryModal;
window.sortReviewHistoryTable = sortReviewHistoryTable;