// Security Review Data Management
// This file manages CRUD operations for security reviews

// Global state for security reviews
let securityReviews = []; // Array to store all security reviews

// Create a new security review
function createSecurityReview(reviewData) {
  try {
    // Validate that the endpoint exists - no fallback allowed
    if (!validateEndpointExists(reviewData.endpointId)) {
      throw new ValidationError(`Endpoint with ID ${reviewData.endpointId} does not exist`);
    }

    // Create and validate the review
    const review = new EndpointSecurityReview(reviewData);

    // Add to the reviews array
    securityReviews.push(review);

    console.log("Security review created successfully:", review);
    return review;
  } catch (error) {
    console.error("Error creating security review:", error);
    throw error;
  }
}

// Get all security reviews for a specific endpoint
function getSecurityReviewsForEndpoint(endpointId) {
  // Only return reviews for endpoints that exist
  if (!validateEndpointExists(endpointId)) {
    return [];
  }
  
  return securityReviews
    .filter((review) => review.endpointId === endpointId)
    .sort((a, b) => new Date(b.reviewDateTime) - new Date(a.reviewDateTime));
}

// Get the latest security review for an endpoint
function getLatestSecurityReview(endpointId) {
  const reviews = getSecurityReviewsForEndpoint(endpointId);
  return reviews.length > 0 ? reviews[0] : null;
}

// Get all security reviews
function getAllSecurityReviews() {
  // Filter out reviews for endpoints that no longer exist
  return securityReviews
    .filter((review) => validateEndpointExists(review.endpointId))
    .sort((a, b) => new Date(b.reviewDateTime) - new Date(a.reviewDateTime));
}

// Filter security reviews by criteria
function filterSecurityReviews(criteria = {}) {
  // Start with reviews for existing endpoints only
  let filtered = securityReviews.filter((review) => validateEndpointExists(review.endpointId));

  // Filter by endpoint ID
  if (criteria.endpointId) {
    filtered = filtered.filter((review) => review.endpointId === criteria.endpointId);
  }

  // Filter by security status
  if (criteria.securityStatus && criteria.securityStatus.length > 0) {
    filtered = filtered.filter((review) =>
      criteria.securityStatus.includes(review.securityStatus)
    );
  }

  // Filter by reviewer
  if (criteria.reviewerUsername) {
    const searchTerm = criteria.reviewerUsername.toLowerCase();
    filtered = filtered.filter((review) =>
      review.reviewerUsername.toLowerCase().includes(searchTerm)
    );
  }

  // Filter by date range
  if (criteria.startDate) {
    const startDate = new Date(criteria.startDate);
    filtered = filtered.filter((review) => new Date(review.reviewDateTime) >= startDate);
  }

  if (criteria.endDate) {
    const endDate = new Date(criteria.endDate);
    endDate.setHours(23, 59, 59, 999); // End of day
    filtered = filtered.filter((review) => new Date(review.reviewDateTime) <= endDate);
  }

  // Sort by date (newest first)
  return filtered.sort(
    (a, b) => new Date(b.reviewDateTime) - new Date(a.reviewDateTime)
  );
}

// Get security review statistics
function getSecurityReviewStats() {
  // Only count reviews for existing endpoints
  const validReviews = securityReviews.filter((review) => validateEndpointExists(review.endpointId));
  
  const stats = {
    totalReviews: validReviews.length,
    statusCounts: {},
    endpointsWithReviews: new Set(),
    reviewerCounts: {},
  };

  // Initialize status counts
  getValidSecurityStatuses().forEach((status) => {
    stats.statusCounts[status] = 0;
  });

  // Calculate statistics
  validReviews.forEach((review) => {
    // Count by status
    if (stats.statusCounts.hasOwnProperty(review.securityStatus)) {
      stats.statusCounts[review.securityStatus]++;
    }

    // Track unique endpoints
    stats.endpointsWithReviews.add(review.endpointId);

    // Count by reviewer
    if (stats.reviewerCounts[review.reviewerUsername]) {
      stats.reviewerCounts[review.reviewerUsername]++;
    } else {
      stats.reviewerCounts[review.reviewerUsername] = 1;
    }
  });

  stats.endpointsWithReviews = stats.endpointsWithReviews.size;

  return stats;
}

// Update an existing security review
function updateSecurityReview(reviewId, updateData) {
  try {
    const reviewIndex = securityReviews.findIndex(review => review.id === reviewId);
    
    if (reviewIndex === -1) {
      throw new Error(`Security review with ID ${reviewId} not found`);
    }

    // Create updated review data
    const currentReview = securityReviews[reviewIndex];
    const updatedReviewData = {
      ...currentReview.toJSON(),
      ...updateData,
      id: reviewId // Ensure ID doesn't change
    };

    // Create new review instance with validation
    const updatedReview = new EndpointSecurityReview(updatedReviewData);

    // Replace the review in the array
    securityReviews[reviewIndex] = updatedReview;

    console.log("Security review updated successfully:", updatedReview);
    return updatedReview;
  } catch (error) {
    console.error("Error updating security review:", error);
    throw error;
  }
}

// Delete a security review
function deleteSecurityReview(reviewId) {
  try {
    const reviewIndex = securityReviews.findIndex(review => review.id === reviewId);
    
    if (reviewIndex === -1) {
      throw new Error(`Security review with ID ${reviewId} not found`);
    }

    const deletedReview = securityReviews.splice(reviewIndex, 1)[0];
    
    console.log("Security review deleted successfully:", deletedReview);
    return deletedReview;
  } catch (error) {
    console.error("Error deleting security review:", error);
    throw error;
  }
}

// Clear all security reviews (for testing or reset purposes)
function clearAllSecurityReviews() {
  const count = securityReviews.length;
  securityReviews = [];
  console.log(`Cleared ${count} security reviews`);
  return count;
}

// Import security reviews from external data
function importSecurityReviews(reviewsData) {
  try {
    if (!Array.isArray(reviewsData)) {
      throw new Error("Reviews data must be an array");
    }

    const importedReviews = [];
    const errors = [];

    reviewsData.forEach((reviewData, index) => {
      try {
        const review = new EndpointSecurityReview(reviewData);
        importedReviews.push(review);
      } catch (error) {
        errors.push({ index, error: error.message });
      }
    });

    // Add successfully imported reviews
    securityReviews.push(...importedReviews);

    const result = {
      imported: importedReviews.length,
      errors: errors.length,
      errorDetails: errors
    };

    console.log("Security reviews import completed:", result);
    return result;
  } catch (error) {
    console.error("Error importing security reviews:", error);
    throw error;
  }
}

// Export security reviews data
function exportSecurityReviews() {
  return securityReviews.map(review => review.toJSON());
}

// Clean up orphaned reviews (remove reviews for endpoints that no longer exist)
function cleanupOrphanedReviews() {
  const initialCount = securityReviews.length;
  securityReviews = securityReviews.filter((review) => validateEndpointExists(review.endpointId));
  const finalCount = securityReviews.length;
  const removedCount = initialCount - finalCount;
  
  if (removedCount > 0) {
    console.log(`Cleaned up ${removedCount} orphaned security review(s) for non-existent endpoints`);
  }
  
  return removedCount;
}

// Export for browser environment
if (typeof window !== "undefined") {
  window.securityReviews = securityReviews;
  window.createSecurityReview = createSecurityReview;
  window.getSecurityReviewsForEndpoint = getSecurityReviewsForEndpoint;
  window.getLatestSecurityReview = getLatestSecurityReview;
  window.getAllSecurityReviews = getAllSecurityReviews;
  window.filterSecurityReviews = filterSecurityReviews;
  window.getSecurityReviewStats = getSecurityReviewStats;
  window.updateSecurityReview = updateSecurityReview;
  window.deleteSecurityReview = deleteSecurityReview;
  window.clearAllSecurityReviews = clearAllSecurityReviews;
  window.importSecurityReviews = importSecurityReviews;
  window.exportSecurityReviews = exportSecurityReviews;
  window.cleanupOrphanedReviews = cleanupOrphanedReviews;
}

// Export for module environment
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    securityReviews,
    createSecurityReview,
    getSecurityReviewsForEndpoint,
    getLatestSecurityReview,
    getAllSecurityReviews,
    filterSecurityReviews,
    getSecurityReviewStats,
    updateSecurityReview,
    deleteSecurityReview,
    clearAllSecurityReviews,
    importSecurityReviews,
    exportSecurityReviews,
    cleanupOrphanedReviews
  };
}
