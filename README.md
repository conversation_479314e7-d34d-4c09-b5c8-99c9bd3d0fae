# ApiEndpointExplorer_v3

A sophisticated web-based tool for visualizing and analyzing API endpoint changes between different versions. This application provides comprehensive features for developers, DevOps engineers, and technical leads to review API diffs, conduct security reviews, and understand the impact of changes across API versions.

## Application Overview

### Key Features

- **Interactive API Diff Visualization**: Compare endpoint changes with color-coded indicators (Added/Removed/Modified/Unmodified)
- **Advanced Filtering System**: Multi-criteria filtering by HTTP methods, policies, security status, and more
- **Security Review Workflow**: Built-in security review process with history tracking and audit trails
- **Responsive Grid Interface**: Sortable, resizable columns with detailed endpoint information
- **Real-time Search**: Global text search across all endpoint properties
- **Customizable Layout**: Resizable panels, adjustable font sizes, and compact view options
- **Detail View**: Comprehensive endpoint comparison with old vs. new configurations
- **Export Capabilities**: Data export and standalone HTML generation

### Target Users

- API Developers analyzing endpoint changes
- DevOps Engineers validating deployment readiness
- QA Engineers identifying test coverage requirements
- Technical Leads reviewing team API changes
- Security Teams conducting endpoint security reviews

## Quick Start

The application needs to be served via HTTP to work properly (due to component loading). You have multiple options:

### Option A: Create Standalone Version (Easiest)

```bash
# Double-click build-standalone.bat on Windows
# Or run manually:
python build-standalone.py
```

Then open `index-standalone.html` directly in your browser (no server needed).

### Option B: Use HTTP Server

#### Using Python (Recommended)

```bash
# Double-click start-server.bat on Windows
# Or run manually:
python server.py
```

#### Using Node.js

```bash
npm install
npm start
```

#### Using any HTTP server

```bash
# Examples:
npx http-server -p 8000
php -S localhost:8000
```

Then open your browser to: <http://localhost:8000/index.html>

## Application Architecture

### Frontend Technology Stack

- **Pure JavaScript**: No framework dependencies, modern ES6+ JavaScript
- **Bootstrap 5**: Responsive UI components and styling
- **Component-Based Architecture**: Modular HTML components with dynamic loading
- **CSS Variables**: Dynamic theming and responsive design
- **Local Storage**: Client-side state persistence

### Backend Integration (Optional)

- **.NET Core Web API**: SecurityReviewApi for persistent security review data
- **Swagger Documentation**: Interactive API documentation at `/swagger`
- **CORS Enabled**: Cross-origin support for frontend integration
- **File-Based Storage**: JSON file persistence for security review data

### Data Structure

The application processes JSON files containing API endpoint diff information with the following structure:

```json
{
  "metadata": {
    "createdAt": "2025-05-29T03:03:12.1642406+03:00",
    "totalCount": 468,
    "addedCount": 33,
    "removedCount": 6,
    "modifiedCount": 132,
    "unmodifiedCount": 297
  },
  "endpointDiffs": [
    {
      "diffType": "Added|Removed|Modified|Unmodified",
      "newValue": { /* endpoint definition */ },
      "oldValue": { /* previous endpoint definition (for modified) */ }
    }
  ]
}
```

## Core Features

### Data Visualization

- **Color-Coded Grid**: Visual indicators for different change types
  - 🟢 Green: Added endpoints
  - 🔴 Red: Removed endpoints  
  - 🟡 Yellow: Modified endpoints
  - ⚪ Default: Unmodified endpoints
- **Sortable Columns**: Click headers to sort by Type, HTTP Methods, Route, Policy, etc.
- **Resizable Layout**: Drag panel edges to customize workspace
- **Tooltip System**: Hover for quick endpoint information

### Advanced Filtering

- **Global Search**: Text search across all endpoint properties
- **Diff Type Filters**: Show/hide by change status
- **HTTP Method Filters**: Filter by GET, POST, PUT, DELETE, etc.
- **Policy Filters**: Filter by authorization policies (AllowAnonymous, Default, etc.)
- **Security Status**: Filter by security review status
- **API Client Inclusion**: Show endpoints included/excluded from client generation
- **Quick Toggles**: Double-click filter headings to toggle all options

### Security Review System

- **Review Workflow**: Structured security assessment process
- **Status Tracking**: Monitor review progress and completion
- **History Management**: Complete audit trail of all reviews
- **Advanced Search**: Complex queries across security review data
- **Bulk Operations**: Mass review operations for efficiency
- **Data Persistence**: Reviews saved to backend API or local storage

### User Experience Features

- **Responsive Design**: Optimized for desktop, tablet, and mobile
- **Customizable Interface**: Adjustable font sizes, compact view, text wrapping
- **Keyboard Navigation**: Full keyboard accessibility support
- **Detail View**: Comprehensive endpoint information panel
- **Export Options**: Generate standalone HTML or export data
- **Progressive Loading**: Efficient handling of large datasets

## Development and Deployment

### Local Development

```bash
# Clone the repository
git clone <repository-url>
cd ApiEndpointExplorer_v3

# Start the development server
python server.py
# OR
npm install && npm start

# Optional: Run the .NET API backend
cd SecurityReviewApi
dotnet run
```

### Building for Production

```bash
# Create standalone version (no server required)
python build-standalone.py

# This generates index-standalone.html that can be opened directly in browsers
```

### Project Structure

```text
├── components/           # HTML component files
│   ├── left-menu.html   # Filter panel
│   ├── main-content.html # Data grid
│   ├── detail-view.html # Endpoint details
│   └── *.html           # Other modals and components
├── js/                  # JavaScript modules
│   ├── app-main.js      # Application initialization
│   ├── data-loader.js   # Data processing
│   ├── grid-manager.js  # Grid rendering
│   ├── security-review/ # Security review system
│   └── *.js            # Other functional modules
├── css/                 # Stylesheets
├── data/               # Sample data files
├── SecurityReviewApi/  # .NET Core backend (optional)
└── docs/              # Documentation
```

## Troubleshooting

If you see "Error Loading Application" when opening index.html directly in your browser, this is because modern browsers block loading local files via JavaScript for security reasons. You must use one of the HTTP server options above.

## File Structure

- `index.html` - Main application entry point
- `components/` - HTML component files
- `js/` - JavaScript modules
- `css/` - Stylesheets
- `data/` - Sample data files
- `SecurityReviewApi/` - .NET API backend (optional)
