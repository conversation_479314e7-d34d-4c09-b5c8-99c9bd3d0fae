---
inclusion: fileMatch
fileMatchPattern: ['**/*.jsx', '**/*.tsx', '**/react/**']
---

# React Development Guidelines

## Component Structure

- Use functional components with hooks instead of class components
- Separate business logic from UI components using custom hooks
- Follow the container/presentational pattern where containers handle data fetching and state
- Keep components small and focused on a single responsibility
- Use prop destructuring for cleaner component definitions
- Implement proper prop validation with PropTypes or TypeScript

## State Management

- Use React Context for global state that needs to be accessed by multiple components
- Prefer useState for local component state that doesn't need to be shared
- Use useReducer for complex state logic with multiple sub-values or when next state depends on previous
- Avoid prop drilling by using context or composition
- Consider using state machines for complex UI states

## Performance Optimization

- Memoize expensive calculations with useMemo
- Prevent unnecessary re-renders with React.memo for pure components
- Use useCallback for event handlers passed to child components
- Implement virtualization for long lists (react-window or react-virtualized)
- Use lazy loading with React.lazy() and Suspense for code splitting
- Avoid inline function definitions in render methods

## Styling Approach

- Use CSS modules for component-specific styling
- Follow BEM-like naming conventions for CSS classes
- Maintain consistent spacing and layout using design tokens
- Use responsive design principles with mobile-first approach

## Testing Guidelines

- Write unit tests for all components using React Testing Library
- Focus on testing behavior rather than implementation details
- Use mock service worker (MSW) for API mocking
- Implement snapshot tests sparingly and only for stable components

## Accessibility

- Ensure all interactive elements are keyboard accessible
- Use semantic HTML elements appropriately
- Include proper ARIA attributes when necessary
- Maintain sufficient color contrast ratios
- Test with screen readers

## Project Integration Notes

**Important**: This project primarily uses vanilla JavaScript as specified in the tech stack.
These React guidelines should only be applied when working with React components that may be
integrated in specific parts of the application or for future React migrations.
