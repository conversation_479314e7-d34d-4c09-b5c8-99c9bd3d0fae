<!-- Advanced Security Review Search Modal -->
<div
  class="modal fade"
  id="advancedSecuritySearchModal"
  tabindex="-1"
  aria-labelledby="advancedSecuritySearchModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-xl modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="advancedSecuritySearchModalLabel">
          <i class="bi bi-search"></i> Advanced Security Review Search
        </h5>
        <button
          type="button"
          class="btn-close btn-close-white"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <!-- Search Filters -->
        <div class="card mb-4">
          <div class="card-header">
            <h6 class="mb-0">
              <i class="bi bi-funnel"></i> Search Filters
            </h6>
          </div>
          <div class="card-body">
            <form id="advancedSearchForm">
              <div class="row">
                <!-- Endpoint Search -->
                <div class="col-md-6 mb-3">
                  <label for="search-endpoint" class="form-label"
                    >Endpoint (Route/Method)</label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="search-endpoint"
                    placeholder="e.g., /api/users, GET, POST /api/orders"
                  />
                  <div class="form-text">
                    Search by route path or HTTP method
                  </div>
                </div>

                <!-- Reviewer Search -->
                <div class="col-md-6 mb-3">
                  <label for="search-reviewer" class="form-label"
                    >Reviewer</label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="search-reviewer"
                    placeholder="e.g., john.doe, jane.smith"
                  />
                  <div class="form-text">Search by reviewer username</div>
                </div>
              </div>

              <div class="row">
                <!-- Notes Search -->
                <div class="col-md-12 mb-3">
                  <label for="search-notes" class="form-label"
                    >Review Notes</label
                  >
                  <input
                    type="text"
                    class="form-control"
                    id="search-notes"
                    placeholder="Search within review notes content..."
                  />
                  <div class="form-text">
                    Search for specific text within security review notes
                  </div>
                </div>
              </div>

              <div class="row">
                <!-- Security Status Filter -->
                <div class="col-md-6 mb-3">
                  <label class="form-label">Security Status</label>
                  <div
                    class="border rounded p-2"
                    style="max-height: 150px; overflow-y: auto"
                  >
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="search-status-compliant"
                        value="Compliant"
                        checked
                      />
                      <label
                        class="form-check-label"
                        for="search-status-compliant"
                      >
                        <span
                          class="security-status security-status-compliant"
                          >Compliant</span
                        >
                      </label>
                    </div>
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="search-status-non-compliant"
                        value="Non-Compliant"
                        checked
                      />
                      <label
                        class="form-check-label"
                        for="search-status-non-compliant"
                      >
                        <span
                          class="security-status security-status-non-compliant"
                          >Non-Compliant</span
                        >
                      </label>
                    </div>
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="search-status-risk-accepted"
                        value="Risk Accepted"
                        checked
                      />
                      <label
                        class="form-check-label"
                        for="search-status-risk-accepted"
                      >
                        <span
                          class="security-status security-status-risk-accepted"
                          >Risk Accepted</span
                        >
                      </label>
                    </div>
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="search-status-under-review"
                        value="Under Review"
                        checked
                      />
                      <label
                        class="form-check-label"
                        for="search-status-under-review"
                      >
                        <span
                          class="security-status security-status-under-review"
                          >Under Review</span
                        >
                      </label>
                    </div>
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="search-status-critical"
                        value="Critical Vulnerability"
                        checked
                      />
                      <label
                        class="form-check-label"
                        for="search-status-critical"
                      >
                        <span
                          class="security-status security-status-critical-vulnerability"
                          >Critical Vulnerability</span
                        >
                      </label>
                    </div>
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="search-status-must-review"
                        value="MUST REVIEW"
                        checked
                      />
                      <label
                        class="form-check-label"
                        for="search-status-must-review"
                      >
                        <span
                          class="security-status security-status-must-review"
                          >MUST REVIEW</span
                        >
                      </label>
                    </div>
                  </div>
                  <div class="mt-2">
                    <button
                      type="button"
                      class="btn btn-sm btn-outline-secondary"
                      id="select-all-statuses"
                    >
                      Select All
                    </button>
                    <button
                      type="button"
                      class="btn btn-sm btn-outline-secondary"
                      id="clear-all-statuses"
                    >
                      Clear All
                    </button>
                  </div>
                </div>

                <!-- Date Range Filter -->
                <div class="col-md-6 mb-3">
                  <label class="form-label">Date Range</label>
                  <div class="row">
                    <div class="col-6">
                      <label
                        for="search-date-from"
                        class="form-label text-muted small"
                        >From</label
                      >
                      <input
                        type="date"
                        class="form-control"
                        id="search-date-from"
                      />
                    </div>
                    <div class="col-6">
                      <label
                        for="search-date-to"
                        class="form-label text-muted small"
                        >To</label
                      >
                      <input
                        type="date"
                        class="form-control"
                        id="search-date-to"
                      />
                    </div>
                  </div>
                  <div class="form-text">Filter reviews by review date</div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="row">
                <div class="col-12">
                  <div class="d-flex gap-2">
                    <button
                      type="button"
                      class="btn btn-primary"
                      id="execute-search"
                    >
                      <i class="bi bi-search"></i> Search Reviews
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-secondary"
                      id="clear-search"
                    >
                      <i class="bi bi-arrow-clockwise"></i> Clear Filters
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-success"
                      id="export-results"
                      disabled
                    >
                      <i class="bi bi-download"></i> Export Results
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>

        <!-- Search Results -->
        <div class="card">
          <div
            class="card-header d-flex justify-content-between align-items-center"
          >
            <h6 class="mb-0">
              <i class="bi bi-table"></i> Search Results
              <span id="results-count" class="badge bg-secondary ms-2"
                >0</span
              >
            </h6>
            <div
              class="btn-group btn-group-sm"
              role="group"
              aria-label="Sort options"
            >
              <button
                type="button"
                class="btn btn-outline-secondary results-sort-btn"
                data-sort-by="date"
                data-sort-order="desc"
              >
                Date ↓
              </button>
              <button
                type="button"
                class="btn btn-outline-secondary results-sort-btn"
                data-sort-by="endpoint"
              >
                Endpoint
              </button>
              <button
                type="button"
                class="btn btn-outline-secondary results-sort-btn"
                data-sort-by="method"
              >
                Method
              </button>
              <button
                type="button"
                class="btn btn-outline-secondary results-sort-btn"
                data-sort-by="reviewer"
              >
                Reviewer
              </button>
              <button
                type="button"
                class="btn btn-outline-secondary results-sort-btn"
                data-sort-by="status"
              >
                Status
              </button>
            </div>
          </div>
          <div class="card-body p-0">
            <!-- No Results Message -->
            <div id="no-search-results" class="alert alert-info m-3 d-none">
              <i class="bi bi-info-circle"></i> No security reviews found
              matching your search criteria. <br /><small class="text-muted"
                >Try adjusting your filters or clearing them to see all
                reviews.</small
              >
            </div>

            <!-- Results Table -->
            <div id="search-results-container" class="d-none">
              <div class="table-responsive" style="max-height: 500px">
                <table
                  class="table table-hover table-striped mb-0"
                  id="search-results-table"
                >
                  <thead class="table-dark sticky-top">
                    <tr>
                      <th style="width: 15%">Date/Time</th>
                      <th style="width: 20%">Endpoint</th>
                      <th style="width: 10%">Method</th>
                      <th style="width: 12%">Reviewer</th>
                      <th style="width: 15%">Status</th>
                      <th style="width: 28%">Notes</th>
                    </tr>
                  </thead>
                  <tbody id="search-results-tbody">
                    <!-- Search results will be populated here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-secondary"
          data-bs-dismiss="modal"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div>
