const fs = require('fs');
const path = require('path');

const inputFile = path.join(__dirname, '../data/diff-Mof.eUrzad-endpoints_20250529_004252.json');
const outputFile = path.join(__dirname, '../data/anon.json');

// Read and parse the JSON file
const data = JSON.parse(fs.readFileSync(inputFile, 'utf8'));

// Try to find the array of endpoints
let endpoints;
if (Array.isArray(data)) {
    endpoints = data;
} else if (typeof data === 'object' && data !== null) {
    // Try common keys
    const possibleKeys = Object.keys(data).filter(key => Array.isArray(data[key]));
    if (possibleKeys.length > 0) {
        endpoints = data[possibleKeys[0]];
    } else {
        console.error('No array found in JSON file. Top-level keys:', Object.keys(data));
        process.exit(1);
    }
} else {
    console.error('Unexpected JSON structure.');
    process.exit(1);
}

// Extract unique controller names from both newValue and oldValue
const controllers = new Set();
for (const item of endpoints) {
    if (item.newValue && item.newValue.controller) {
        const name = item.newValue.controller.replace(/Controller$/, '');
        item.newValue.controller = name; // Update the controller name in newValue
        controllers.add(name);
    }
    if (item.oldValue && item.oldValue.controller) {
        const name = item.oldValue.controller.replace(/Controller$/, '');
        item.oldValue.controller = name; // Update the controller name in oldValue
        controllers.add(name);
    }
}

// Helper to generate a random string of given length
function randomString(name) {
    const length = name.length;
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    // First two letters from name, capitalized
    const firstTwo = name.slice(0, 2).toUpperCase();
    // Next two: length, zero-padded
    const lenStr = length.toString().padStart(2, '0');
    let result = firstTwo + lenStr;
    for (let i = 4; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// Helper to randomize words in a string. A word is a sequence of alphanumeric characters and underscores.
function randomizeWords(str) {
    if (typeof str !== 'string') return str;
    // The regex now includes '|', '\', '{', '}', and other separators by splitting the string and randomizing parts.
    // Split on separators including braces, preserving them
    return str.split(/(\{|\}|[|.,<>()=\s\\])/).map(part => {
        // If this part is a separator (including braces), preserve it
        if (/^(\{|\}|[|.,<>()=\s\\])$/.test(part)) return part;
        return part.replace(/[a-zA-Z0-9_]+/g, match => {
            if (match === 'XXX' || match === 'YYY') {
                return match;
            }
            // Case insensitive replacements
            if (match.toLowerCase() === 'mof') {
                return 'XXX';
            }
            if (match.toLowerCase() === 'eurzad') {
                return 'YYY';
            }
            if (match.toLowerCase() === 'datahub') {
                return 'ZZZ';
            }
            if (match.toLowerCase() === 'eforms') {
                return 'AAA';
            }
            if (match.toLowerCase() === 'frontoffice') {
                return 'BBB';
            }
            if (match.toLowerCase() === 'api') {
                return 'CCC';
            }
            return randomString(match);
        });
    }).join('');
}

// Build mapping from original name to random string
const nameMap = {};
for (const name of controllers) {
    nameMap[name] = randomString(name);
}

// Helper to replace controller names in any object
function processData(obj) {
    if (!obj || typeof obj !== 'object') {
        return;
    }

    if (Array.isArray(obj)) {
        obj.forEach(item => processData(item));
        return;
    }

    for (const key in obj) {
        if (!obj.hasOwnProperty(key)) {
            continue;
        }

        const lowerKey = key.toLowerCase();
        const value = obj[key];

        if (typeof value === 'string') {
            if (lowerKey === 'filename') {
                const parts = value.split(/(\.cs$)/);
                if (parts.length === 3 && parts[1] === '.cs') {
                    obj[key] = randomizeWords(parts[0]) + parts[1];
                } else {
                    obj[key] = randomizeWords(value);
                }
            } else if (lowerKey === 'projectname' ||
                    lowerKey === 'response' ||
                    lowerKey === 'fieldname' ||
                    lowerKey === 'oldvalue' ||
                    lowerKey === 'newvalue' ||
                    lowerKey === 'policy' ||
                    lowerKey === 'namespace') {
                obj[key] = randomizeWords(value);
            } else if (lowerKey === 'route') {
                obj[key] = randomizeWords(value);
            } else if (lowerKey === 'actionname') {
                obj[key] = randomString(value);
            }

            // Generic replacements after specific ones
            for (const orig in nameMap) {
                obj[key] = obj[key].replace(new RegExp(orig, 'gi'), nameMap[orig]);
            }
        } else if (Array.isArray(value) && (lowerKey === 'requestparameters' || lowerKey === 'otherattributes')) {
            obj[key] = value.map(item => (typeof item === 'string' ? randomizeWords(item) : item));
        } else if (typeof value === 'object') {
            processData(value);
        }
    }
}

// Deep clone data to avoid mutating original
const outputData = JSON.parse(JSON.stringify(data));
processData(outputData);

// Write transformed data to output file
fs.writeFileSync(outputFile, JSON.stringify(outputData, null, 2), 'utf8');
console.log('Transformation complete. Output written to', outputFile);
