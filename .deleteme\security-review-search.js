// Advanced Security Review Search Functionality
// This file implements the advanced search interface for security reviews across all endpoints

// Global state for search functionality
let currentSearchResults = [];
let currentSearchCriteria = {};

// Helper function to extract endpoint information from complex data structure
function extractEndpointInfo(endpoint) {
  if (!endpoint) {
    return {
      route: "Unknown Route",
      methods: ["Unknown"],
      methodString: "Unknown"
    };
  }
  
  // The endpoint should now be the properly extracted data from getEndpointById
  // So we can access properties directly
  const route = endpoint.route || "Unknown Route";
  const methods = endpoint.httpMethods && Array.isArray(endpoint.httpMethods) 
    ? endpoint.httpMethods 
    : ["Unknown"];
  
  return {
    route: route,
    methods: methods,
    methodString: methods.join(", ")
  };
}

// Initialize advanced security review search functionality
function initAdvancedSecuritySearch() {
  console.log("Initializing advanced security review search functionality");
  
  // These functions will be called from app-main.js after components are loaded
  // No longer using DOMContentLoaded here to avoid conflicts
}

// Initialize event listeners for the advanced search button
function initSearchEventListeners() {
  // Advanced search button click handler
  const advancedSearchBtn = document.getElementById("advanced-search-btn");
  console.log("Advanced search button found:", !!advancedSearchBtn);
  
  if (advancedSearchBtn) {
    advancedSearchBtn.addEventListener("click", showAdvancedSearchModal);
    console.log("Advanced search event listener attached");
  }
}

// Initialize form handlers for the search modal
function initSearchFormHandlers() {
  // Execute search button
  const executeSearchBtn = document.getElementById("execute-search");
  if (executeSearchBtn) {
    executeSearchBtn.addEventListener("click", executeAdvancedSearch);
  }
  
  // Clear search button
  const clearSearchBtn = document.getElementById("clear-search");
  if (clearSearchBtn) {
    clearSearchBtn.addEventListener("click", clearSearchFilters);
  }
  
  // Export results button
  const exportResultsBtn = document.getElementById("export-results");
  if (exportResultsBtn) {
    exportResultsBtn.addEventListener("click", exportSearchResults);
  }
  
  // Select all statuses button
  const selectAllStatusesBtn = document.getElementById("select-all-statuses");
  if (selectAllStatusesBtn) {
    selectAllStatusesBtn.addEventListener("click", selectAllSecurityStatuses);
  }
  
  // Clear all statuses button
  const clearAllStatusesBtn = document.getElementById("clear-all-statuses");
  if (clearAllStatusesBtn) {
    clearAllStatusesBtn.addEventListener("click", clearAllSecurityStatuses);
  }
  
  // Real-time search on form input changes
  const searchForm = document.getElementById("advancedSearchForm");
  if (searchForm) {
    const inputs = searchForm.querySelectorAll("input");
    inputs.forEach(input => {
      if (input.type === "text" || input.type === "date") {
        input.addEventListener("input", debounce(executeAdvancedSearch, 500));
      } else if (input.type === "checkbox") {
        input.addEventListener("change", executeAdvancedSearch);
      }
    });
  }
}

// Initialize results table handlers
function initResultsTableHandlers() {
  // Sort buttons for results table
  document.addEventListener("click", function(e) {
    const sortBtn = e.target.closest(".results-sort-btn");
    if (!sortBtn) return;
    
    const sortBy = sortBtn.getAttribute("data-sort-by");
    let sortOrder = sortBtn.getAttribute("data-sort-order") || "asc";
    
    // Toggle sort order if clicking the same button again
    if (sortBtn.classList.contains("active")) {
      sortOrder = sortOrder === "asc" ? "desc" : "asc";
    }
    
    // Sort the results
    sortSearchResults(sortBy, sortOrder);
    
    // Update sort button states
    updateResultsSortButtonStates(sortBy, sortOrder);
  });
  
  // Table header click handlers
  document.addEventListener("click", function(e) {
    const headerCell = e.target.closest("#search-results-table th");
    if (!headerCell) return;
    
    // Determine which column was clicked
    const headerIndex = Array.from(headerCell.parentNode.children).indexOf(headerCell);
    let sortBy;
    
    // Map header index to sort column
    switch (headerIndex) {
      case 0: // Date/Time column
        sortBy = "date";
        break;
      case 1: // Endpoint column
        sortBy = "endpoint";
        break;
      case 2: // Method column
        sortBy = "method";
        break;
      case 3: // Reviewer column
        sortBy = "reviewer";
        break;
      case 4: // Status column
        sortBy = "status";
        break;
      default:
        return; // Don't sort by other columns
    }
    
    // Find the corresponding sort button
    const sortBtn = document.querySelector(`.results-sort-btn[data-sort-by="${sortBy}"]`);
    if (!sortBtn) return;
    
    // Get current sort order
    let sortOrder = sortBtn.getAttribute("data-sort-order") || "asc";
    
    // Toggle sort order if this column is already active
    if (sortBtn.classList.contains("active")) {
      sortOrder = sortOrder === "asc" ? "desc" : "asc";
    }
    
    // Sort the results
    sortSearchResults(sortBy, sortOrder);
    
    // Update sort button states
    updateResultsSortButtonStates(sortBy, sortOrder);
  });
}

// Show the advanced search modal
function showAdvancedSearchModal() {
  console.log("Showing advanced security search modal");
  
  // Get the modal element
  const modal = document.getElementById("advancedSecuritySearchModal");
  console.log("Modal element found:", !!modal);
  
  if (!modal) {
    console.error("Advanced security search modal not found in DOM");
    alert("Advanced security search modal not found. Please check if the modal HTML is included in the page.");
    return;
  }
  
  // Check if Bootstrap is available
  if (typeof bootstrap === 'undefined' || !bootstrap.Modal) {
    console.error("Bootstrap Modal is not available");
    alert("Bootstrap Modal is not available. Please check if Bootstrap JS is properly loaded.");
    return;
  }
  
  // Get the Bootstrap modal instance
  const modalInstance = bootstrap.Modal.getOrCreateInstance(modal);
  
  // Initialize the form with default values
  initializeSearchForm();
  
  // Execute initial search to show all reviews
  executeAdvancedSearch();
  
  // Show the modal
  modalInstance.show();
}

// Initialize the search form with default values
function initializeSearchForm() {
  // Clear text inputs
  document.getElementById("search-endpoint").value = "";
  document.getElementById("search-reviewer").value = "";
  document.getElementById("search-notes").value = "";
  
  // Set date range to last 30 days by default
  const today = new Date();
  const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
  
  document.getElementById("search-date-from").value = "";
  document.getElementById("search-date-to").value = "";
  
  // Check all security status checkboxes by default
  const statusCheckboxes = document.querySelectorAll("#advancedSearchForm input[type='checkbox']");
  statusCheckboxes.forEach(checkbox => {
    checkbox.checked = true;
  });
}

// Execute the advanced search with enhanced error handling
function executeAdvancedSearch() {
  console.log("Executing advanced search");
  
  try {
    // Show loading state
    const resultsContainer = document.getElementById("search-results-container");
    const noResultsDiv = document.getElementById("no-search-results");
    
    if (resultsContainer) {
      const hideOverlay = showLoadingOverlay(resultsContainer, "Searching reviews...");
      
      // Use setTimeout to allow UI to update
      setTimeout(() => {
        try {
          // Get search criteria from the form
          const criteria = getSearchCriteria();
          currentSearchCriteria = criteria;
          
          // Get all security reviews
          const allReviews = getAllSecurityReviews();
          
          if (!Array.isArray(allReviews)) {
            throw new Error("Security reviews data is not available or invalid");
          }
          
          console.log(`Searching through ${allReviews.length} total reviews`);
          
          // Filter reviews based on criteria
          let filteredReviews = filterReviewsByCriteria(allReviews, criteria);
          
          if (!Array.isArray(filteredReviews)) {
            throw new Error("Search filtering failed to return valid results");
          }
          
          // Store current results
          currentSearchResults = filteredReviews;
          
          // Display the results
          displaySearchResults(filteredReviews);
          
          // Update export button state
          const exportBtn = document.getElementById("export-results");
          if (exportBtn) {
            exportBtn.disabled = filteredReviews.length === 0;
            exportBtn.title = filteredReviews.length === 0 
              ? "No results to export" 
              : `Export ${filteredReviews.length} result${filteredReviews.length > 1 ? 's' : ''}`;
          }
          
          console.log(`Search completed: ${filteredReviews.length} results found`);
          
          // Show success message for large result sets
          if (filteredReviews.length > 100) {
            showInfo(`Found ${filteredReviews.length} results. Consider refining your search for better performance.`, {
              persistent: false
            });
          }
          
        } catch (error) {
          console.error("Error during search execution:", error);
          
          // Show error in results area
          displaySearchError(error.message);
          
          // Show toast notification
          showError("Search failed", {
            details: error.message,
            persistent: false,
            showInGrid: false,
            allowRetry: true,
            retryCallback: 'retryAdvancedSearch'
          });
        } finally {
          hideOverlay();
        }
      }, 100);
    } else {
      throw new Error("Search interface not found");
    }
    
  } catch (error) {
    console.error("Error initializing advanced search:", error);
    showError("Cannot perform search", {
      details: error.message,
      persistent: true,
      showInGrid: false
    });
  }
}

// Get search criteria from the form
function getSearchCriteria() {
  const criteria = {};
  
  // Endpoint search
  const endpointSearch = document.getElementById("search-endpoint").value.trim();
  if (endpointSearch) {
    criteria.endpoint = endpointSearch.toLowerCase();
  }
  
  // Reviewer search
  const reviewerSearch = document.getElementById("search-reviewer").value.trim();
  if (reviewerSearch) {
    criteria.reviewer = reviewerSearch.toLowerCase();
  }
  
  // Notes search
  const notesSearch = document.getElementById("search-notes").value.trim();
  if (notesSearch) {
    criteria.notes = notesSearch.toLowerCase();
  }
  
  // Security status filter
  const statusCheckboxes = document.querySelectorAll("#advancedSearchForm input[type='checkbox']:checked");
  const selectedStatuses = Array.from(statusCheckboxes).map(cb => cb.value);
  if (selectedStatuses.length > 0) {
    criteria.securityStatus = selectedStatuses;
  }
  
  // Date range filter
  const dateFrom = document.getElementById("search-date-from").value;
  const dateTo = document.getElementById("search-date-to").value;
  
  if (dateFrom) {
    criteria.startDate = new Date(dateFrom);
  }
  
  if (dateTo) {
    // Set to end of day for the "to" date
    const endDate = new Date(dateTo);
    endDate.setHours(23, 59, 59, 999);
    criteria.endDate = endDate;
  }
  
  return criteria;
}

// Filter reviews by search criteria
function filterReviewsByCriteria(reviews, criteria) {
  return reviews.filter(review => {
    // Filter by endpoint (route or method)
    if (criteria.endpoint) {
      const endpoint = getEndpointById(review.endpointId);
      if (endpoint) {
        const endpointInfo = extractEndpointInfo(endpoint);
        const route = endpointInfo.route.toLowerCase();
        const methodsString = endpointInfo.methods.join(" ").toLowerCase();
        const searchTerm = criteria.endpoint;
        
        if (!route.includes(searchTerm) && !methodsString.includes(searchTerm)) {
          return false;
        }
      } else {
        // If endpoint not found, skip this review
        return false;
      }
    }
    
    // Filter by reviewer
    if (criteria.reviewer) {
      if (!review.reviewerUsername.toLowerCase().includes(criteria.reviewer)) {
        return false;
      }
    }
    
    // Filter by notes content
    if (criteria.notes) {
      const reviewNotes = review.reviewNotes || "";
      if (!reviewNotes.toLowerCase().includes(criteria.notes)) {
        return false;
      }
    }
    
    // Filter by security status
    if (criteria.securityStatus && criteria.securityStatus.length > 0) {
      if (!criteria.securityStatus.includes(review.securityStatus)) {
        return false;
      }
    }
    
    // Filter by date range
    const reviewDate = new Date(review.reviewDateTime);
    
    if (criteria.startDate && reviewDate < criteria.startDate) {
      return false;
    }
    
    if (criteria.endDate && reviewDate > criteria.endDate) {
      return false;
    }
    
    return true;
  });
}

// Display search results in the table with enhanced error handling
function displaySearchResults(results) {
  try {
    const tbody = document.getElementById("search-results-tbody");
    const noResultsDiv = document.getElementById("no-search-results");
    const resultsContainer = document.getElementById("search-results-container");
    const resultsCount = document.getElementById("results-count");
    
    if (!tbody) {
      throw new Error("Search results table not found in the interface");
    }
    
    // Clear existing results
    tbody.innerHTML = "";
    
    // Update results count
    if (resultsCount) {
      resultsCount.textContent = results.length;
    }
    
    // Show/hide elements based on whether there are results
    if (results.length === 0) {
      if (noResultsDiv) {
        noResultsDiv.classList.remove("d-none");
        // Enhance no results message
        const criteria = currentSearchCriteria;
        let message = "No security reviews match your search criteria.";
        
        if (criteria.endpoint || criteria.reviewer || criteria.securityStatus?.length > 0) {
          message += " Try adjusting your filters or clearing some search terms.";
        }
        
        noResultsDiv.innerHTML = `
          <div class="text-center p-4">
            <i class="bi bi-search" style="font-size: 2rem; color: var(--bs-secondary);"></i>
            <h6 class="mt-2 mb-1">No Results Found</h6>
            <p class="text-muted mb-3">${message}</p>
            <button class="btn btn-outline-secondary btn-sm" onclick="clearSearchFilters()">
              <i class="bi bi-x-circle me-1"></i>Clear Filters
            </button>
          </div>`;
      }
      if (resultsContainer) resultsContainer.classList.add("d-none");
      return;
    } else {
      if (noResultsDiv) noResultsDiv.classList.add("d-none");
      if (resultsContainer) resultsContainer.classList.remove("d-none");
    }
    
    // Create rows for each result with error handling
    const validRows = [];
    const errorCount = 0;
    
    results.forEach((review, index) => {
      try {
        const row = createSearchResultRow(review, index);
        validRows.push(row);
      } catch (error) {
        console.error(`Error creating search result row ${index}:`, error);
        errorCount++;
      }
    });
    
    // Add valid rows to table
    validRows.forEach(row => {
      tbody.appendChild(row);
    });
    
    // Show warning if some rows failed
    if (errorCount > 0) {
      const warningRow = document.createElement("tr");
      warningRow.className = "table-warning";
      warningRow.innerHTML = `
        <td colspan="6" class="text-center p-2">
          <small class="text-warning">
            <i class="bi bi-exclamation-triangle me-1"></i>
            ${errorCount} result${errorCount > 1 ? 's' : ''} could not be displayed due to data issues
          </small>
        </td>`;
      tbody.appendChild(warningRow);
    }
    
    // Set initial sort (newest first) with error handling
    try {
      sortSearchResults("date", "desc");
      updateResultsSortButtonStates("date", "desc");
    } catch (error) {
      console.error("Error sorting search results:", error);
      showWarning("Results loaded but sorting failed", {
        details: error.message,
        persistent: false
      });
    }
    
  } catch (error) {
    console.error("Error displaying search results:", error);
    displaySearchError(error.message);
  }
}

// Display search error in the results area
function displaySearchError(errorMessage) {
  const tbody = document.getElementById("search-results-tbody");
  const noResultsDiv = document.getElementById("no-search-results");
  const resultsContainer = document.getElementById("search-results-container");
  
  if (tbody) {
    tbody.innerHTML = `
      <tr>
        <td colspan="6" class="text-center text-danger p-4">
          <div class="alert alert-danger mb-0" role="alert">
            <div class="d-flex align-items-center justify-content-center mb-2">
              <i class="bi bi-exclamation-triangle-fill me-2"></i>
              <strong>Search Error</strong>
            </div>
            <p class="mb-2">${escapeHtml(errorMessage)}</p>
            <button class="btn btn-sm btn-outline-danger" onclick="retryAdvancedSearch()">
              <i class="bi bi-arrow-clockwise me-1"></i>Retry Search
            </button>
          </div>
        </td>
      </tr>`;
  }
  
  if (noResultsDiv) noResultsDiv.classList.add("d-none");
  if (resultsContainer) resultsContainer.classList.remove("d-none");
}

// Create a table row for a search result
function createSearchResultRow(review, index) {
  const row = document.createElement("tr");
  
  // Get endpoint information
  const endpoint = getEndpointById(review.endpointId);
  const endpointInfo = extractEndpointInfo(endpoint);
  const route = endpointInfo.route;
  const method = endpointInfo.methodString;
  
  // Format the date
  const formattedDate = formatReviewDate(review.reviewDateTime);
  const timestamp = new Date(review.reviewDateTime).getTime();
  
  // Create cells
  const dateCell = document.createElement("td");
  dateCell.setAttribute("data-sort-value", timestamp);
  dateCell.textContent = formattedDate;
  
  const endpointCell = document.createElement("td");
  endpointCell.textContent = route;
  endpointCell.title = route; // Full route in tooltip
  
  const methodCell = document.createElement("td");
  methodCell.innerHTML = `<span class="badge bg-secondary">${method}</span>`;
  
  const reviewerCell = document.createElement("td");
  reviewerCell.textContent = review.reviewerUsername;
  
  const statusCell = document.createElement("td");
  const statusClass = getSecurityStatusClass(review.securityStatus);
  statusCell.innerHTML = `<span class="security-status ${statusClass}">${review.securityStatus}</span>`;
  
  const notesCell = document.createElement("td");
  if (!review.reviewNotes || review.reviewNotes.trim() === "") {
    notesCell.innerHTML = '<em class="text-muted">No notes</em>';
  } else {
    // Create expandable notes
    const notesContainer = document.createElement("div");
    notesContainer.className = "review-notes-container";
    
    const notesPreview = document.createElement("div");
    notesPreview.className = "review-notes-preview";
    const previewText = review.reviewNotes.length > 80 
      ? review.reviewNotes.substring(0, 80) + "..." 
      : review.reviewNotes;
    notesPreview.textContent = previewText;
    
    const notesContent = document.createElement("div");
    notesContent.className = "review-notes-content d-none";
    
    // Convert markdown to HTML if available
    if (typeof marked === 'function') {
      notesContent.innerHTML = marked(review.reviewNotes);
    } else {
      notesContent.innerHTML = review.reviewNotes.replace(/\n/g, "<br>");
    }
    
    // Create toggle button if notes are long enough
    if (review.reviewNotes.length > 80) {
      const toggleButton = document.createElement("button");
      toggleButton.className = "btn btn-sm btn-link review-notes-toggle p-0";
      toggleButton.textContent = "Show more";
      toggleButton.setAttribute("data-expanded", "false");
      toggleButton.setAttribute("data-review-index", index);
      
      notesContainer.appendChild(notesPreview);
      notesContainer.appendChild(notesContent);
      notesContainer.appendChild(toggleButton);
    } else {
      notesContainer.appendChild(notesPreview);
    }
    
    notesCell.appendChild(notesContainer);
  }
  
  // Add cells to row
  row.appendChild(dateCell);
  row.appendChild(endpointCell);
  row.appendChild(methodCell);
  row.appendChild(reviewerCell);
  row.appendChild(statusCell);
  row.appendChild(notesCell);
  
  // Add click handler to row for viewing endpoint details
  row.style.cursor = "pointer";
  row.addEventListener("click", function(e) {
    // Don't trigger if clicking on toggle button
    if (e.target.closest(".review-notes-toggle")) return;
    
    // Find and show the endpoint in the main grid
    if (endpoint) {
      // Close the search modal
      const modal = document.getElementById("advancedSecuritySearchModal");
      if (modal) {
        const modalInstance = bootstrap.Modal.getInstance(modal);
        if (modalInstance) {
          modalInstance.hide();
        }
      }
      
      // Show the endpoint in detail view
      if (typeof showDetailView === 'function') {
        showDetailView(endpoint);
      }
    }
  });
  
  return row;
}

// Sort search results
function sortSearchResults(sortBy, sortOrder = "asc") {
  console.log(`Sorting search results by ${sortBy} in ${sortOrder} order`);
  
  const tbody = document.getElementById("search-results-tbody");
  if (!tbody) {
    console.warn("Search results table body not found");
    return;
  }
  
  const rows = Array.from(tbody.querySelectorAll("tr"));
  if (rows.length === 0) {
    console.warn("No rows found in search results table");
    return;
  }
  
  // Define sort functions
  const sortFunctions = {
    date: (a, b) => {
      const aValue = parseInt(a.querySelector("td:first-child").getAttribute("data-sort-value") || "0", 10);
      const bValue = parseInt(b.querySelector("td:first-child").getAttribute("data-sort-value") || "0", 10);
      return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
    },
    endpoint: (a, b) => {
      const aValue = a.querySelector("td:nth-child(2)").textContent.toLowerCase();
      const bValue = b.querySelector("td:nth-child(2)").textContent.toLowerCase();
      return sortOrder === "asc" 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    },
    method: (a, b) => {
      const aValue = a.querySelector("td:nth-child(3)").textContent.toLowerCase();
      const bValue = b.querySelector("td:nth-child(3)").textContent.toLowerCase();
      return sortOrder === "asc" 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    },
    reviewer: (a, b) => {
      const aValue = a.querySelector("td:nth-child(4)").textContent.toLowerCase();
      const bValue = b.querySelector("td:nth-child(4)").textContent.toLowerCase();
      return sortOrder === "asc" 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    },
    status: (a, b) => {
      const aValue = a.querySelector("td:nth-child(5)").textContent.toLowerCase();
      const bValue = b.querySelector("td:nth-child(5)").textContent.toLowerCase();
      return sortOrder === "asc" 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    }
  };
  
  // Sort the rows
  rows.sort(sortFunctions[sortBy] || sortFunctions.date);
  
  // Remove all rows from the table
  while (tbody.firstChild) {
    tbody.removeChild(tbody.firstChild);
  }
  
  // Add the sorted rows back to the table
  rows.forEach(row => {
    tbody.appendChild(row);
  });
}

// Update sort button states for results table
function updateResultsSortButtonStates(activeSortBy, activeSortOrder) {
  const sortButtons = document.querySelectorAll(".results-sort-btn");
  
  sortButtons.forEach(button => {
    const sortBy = button.getAttribute("data-sort-by");
    
    // Reset all buttons
    button.classList.remove("active");
    button.textContent = button.textContent.replace(" ↑", "").replace(" ↓", "");
    
    // Update the active button
    if (sortBy === activeSortBy) {
      button.classList.add("active");
      button.setAttribute("data-sort-order", activeSortOrder);
      button.textContent = button.textContent + (activeSortOrder === "asc" ? " ↑" : " ↓");
    }
  });
}

// Clear search filters
function clearSearchFilters() {
  console.log("Clearing search filters");
  
  // Clear text inputs
  document.getElementById("search-endpoint").value = "";
  document.getElementById("search-reviewer").value = "";
  document.getElementById("search-notes").value = "";
  document.getElementById("search-date-from").value = "";
  document.getElementById("search-date-to").value = "";
  
  // Check all security status checkboxes
  selectAllSecurityStatuses();
  
  // Execute search to show all results
  executeAdvancedSearch();
}

// Select all security status checkboxes
function selectAllSecurityStatuses() {
  const statusCheckboxes = document.querySelectorAll("#advancedSearchForm input[type='checkbox']");
  statusCheckboxes.forEach(checkbox => {
    checkbox.checked = true;
  });
}

// Clear all security status checkboxes
function clearAllSecurityStatuses() {
  const statusCheckboxes = document.querySelectorAll("#advancedSearchForm input[type='checkbox']");
  statusCheckboxes.forEach(checkbox => {
    checkbox.checked = false;
  });
}

// Export search results to CSV with enhanced error handling
function exportSearchResults() {
  console.log("Exporting search results");
  
  try {
    if (!currentSearchResults || currentSearchResults.length === 0) {
      showWarning("No results to export", {
        details: "Please perform a search first to generate results for export.",
        persistent: false
      });
      return;
    }
    
    // Show loading state on export button
    const exportBtn = document.getElementById("export-results");
    const hideLoading = exportBtn ? showLoadingState(exportBtn, "Exporting...") : null;
    
    // Validate data before export
    const validResults = currentSearchResults.filter(result => {
      return result && typeof result === 'object' && result.endpointId;
    });
    
    if (validResults.length === 0) {
      if (hideLoading) hideLoading();
      throw new Error("No valid results found to export");
    }
    
    if (validResults.length !== currentSearchResults.length) {
      showWarning(`${currentSearchResults.length - validResults.length} invalid results excluded from export`, {
        persistent: false
      });
    }
    
    console.log(`Exporting ${validResults.length} valid results`);
    
    // Create CSV content
    const csvContent = generateCSVContent(validResults);
    
    if (!csvContent || csvContent.trim().length === 0) {
      if (hideLoading) hideLoading();
      throw new Error("Failed to generate CSV content");
    }
    
    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    
    if (!blob || blob.size === 0) {
      if (hideLoading) hideLoading();
      throw new Error("Failed to create export file");
    }
    
    const link = document.createElement("a");
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      
      // Generate filename with timestamp and result count
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = `security-review-search-results-${validResults.length}-items-${timestamp}.csv`;
      link.setAttribute("download", filename);
      
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up the URL
      setTimeout(() => URL.revokeObjectURL(url), 1000);
      
      if (hideLoading) hideLoading();
      
      showSuccess(`Successfully exported ${validResults.length} search results`, {
        persistent: false
      });
      
      console.log(`Export completed successfully: ${filename}`);
    } else {
      if (hideLoading) hideLoading();
      throw new Error("Browser does not support file download. Please use a modern browser.");
    }
    
  } catch (error) {
    console.error("Error exporting results:", error);
    
    // Categorize error types
    let errorMessage = "Failed to export search results";
    let errorDetails = error.message;
    
    if (error.message.includes('download')) {
      errorMessage = "Browser download not supported";
      errorDetails = "Your browser doesn't support file downloads. Please try a different browser.";
    } else if (error.message.includes('CSV') || error.message.includes('content')) {
      errorMessage = "Data formatting error";
      errorDetails = "Failed to format the search results for export.";
    } else if (error.message.includes('Blob') || error.message.includes('file')) {
      errorMessage = "File creation error";
      errorDetails = "Failed to create the export file.";
    }
    
    showError(errorMessage, {
      details: errorDetails,
      persistent: true,
      showInGrid: false,
      allowRetry: true,
      retryCallback: 'retryExportResults'
    });
  }
}

// Generate CSV content from search results
function generateCSVContent(results) {
  const headers = [
    "Review Date",
    "Endpoint Route",
    "HTTP Method", 
    "Reviewer",
    "Security Status",
    "Review Notes"
  ];
  
  // Create CSV rows
  const rows = results.map(review => {
    const endpoint = getEndpointById(review.endpointId);
    const endpointInfo = extractEndpointInfo(endpoint);
    const route = endpointInfo.route;
    const method = endpointInfo.methodString;
    
    return [
      formatReviewDate(review.reviewDateTime),
      escapeCSVField(route),
      method,
      escapeCSVField(review.reviewerUsername),
      escapeCSVField(review.securityStatus),
      escapeCSVField(review.reviewNotes || "")
    ];
  });
  
  // Combine headers and rows
  const allRows = [headers, ...rows];
  
  // Convert to CSV string
  return allRows.map(row => row.join(",")).join("\n");
}

// Escape CSV field (handle commas, quotes, newlines)
function escapeCSVField(field) {
  if (typeof field !== 'string') {
    field = String(field);
  }
  
  // If field contains comma, quote, or newline, wrap in quotes and escape internal quotes
  if (field.includes(',') || field.includes('"') || field.includes('\n')) {
    return '"' + field.replace(/"/g, '""') + '"';
  }
  
  return field;
}

// Debounce function for real-time search
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Initialize expand/collapse functionality for search result notes
document.addEventListener("click", function(e) {
  const toggleButton = e.target.closest(".review-notes-toggle");
  if (!toggleButton) return;
  
  // Get the notes container
  const notesContainer = toggleButton.closest(".review-notes-container");
  if (!notesContainer) return;
  
  // Get the preview and content elements
  const preview = notesContainer.querySelector(".review-notes-preview");
  const content = notesContainer.querySelector(".review-notes-content");
  
  // Check if currently expanded
  const isExpanded = toggleButton.getAttribute("data-expanded") === "true";
  
  if (isExpanded) {
    // Collapse
    if (preview) preview.classList.remove("d-none");
    if (content) content.classList.add("d-none");
    toggleButton.textContent = "Show more";
    toggleButton.setAttribute("data-expanded", "false");
  } else {
    // Expand
    if (preview) preview.classList.add("d-none");
    if (content) content.classList.remove("d-none");
    toggleButton.textContent = "Show less";
    toggleButton.setAttribute("data-expanded", "true");
  }
  
  // Prevent row click event
  e.stopPropagation();
});

// Retry functions for error recovery
window.retryAdvancedSearch = function() {
  executeAdvancedSearch();
};

window.retryExportResults = function() {
  exportSearchResults();
};

// Export functions to global scope
window.initAdvancedSecuritySearch = initAdvancedSecuritySearch;
window.showAdvancedSearchModal = showAdvancedSearchModal;
window.executeAdvancedSearch = executeAdvancedSearch;
window.exportSearchResults = exportSearchResults;
window.extractEndpointInfo = extractEndpointInfo;
window.filterReviewsByCriteria = filterReviewsByCriteria;
window.displaySearchError = displaySearchError;

// Auto-initialize when script loads
initAdvancedSecuritySearch();