using System.Text.Json.Serialization;

namespace SecurityReviewApi.Models
{
    public class Metadata
    {
        [JsonPropertyName("createdAt")]
        public DateTime CreatedAt { get; set; }

        [JsonPropertyName("originalDataFile")]
        public string OriginalDataFile { get; set; }

        [JsonPropertyName("totalReviews")]
        public int TotalReviews { get; set; }

        [JsonPropertyName("lastModified")]
        public DateTime LastModified { get; set; }
    }
}
