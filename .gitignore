# =============================
# .NET
# =============================

# Build Folders
**/bin/
**/obj/

# User-specific files
*.user
*.userosscache
*.suo
*.sln.docstates

# Mono auto-generated files
mono_crash.*

# ASP.NET Scaffolding
ScaffoldingReadMe.txt

# StyleCop
StyleCopReport.xml

# Files built by Visual Studio
*.dll
*.exe
*.pdb
*.cache
*.log
*.ilk
*.ncb
*.sdf
*.opensdf
*.VC.db
*.opendb

# Rider and ReSharper
.idea/
*.sln.iml
_ReSharper.Caches/
*.DotSettings.user
*.resharper

# Visual Studio Code
.vscode/
.history/
*.code-workspace

# Visual Studio
.vs/
*.VC.db
*.cache
ipch/
Ankh.NoLoad

# Build Results
[Bb]uild/
[Bb]in/
[Oo]bj/
[Dd]ebug*/
[Rr]elease*/
x64/
x86/

# NuGet Packages
packages/
*.nupkg
*.snupkg
*.nuspec
!.nuget/packages/build/

# Test Results
TestResults/
*.trx
*.coverage
*.coveragexml

# Continuous Integration
_TeamCity/
.azure/
.aci/
_TeamCity/

# Backup & Report Files
*.bak
*.out
*.publishsettings
*.sql
*.swp
*.tmp
*.temp
*.tss
*.log

# Installer Files
*.msi
*.msix
*.appx

# Packaging
*.packaging

# Private Generative Files
*.g.cs
*.g.i.cs

# Resharper
*.resharper

# DotCover
*.dotCover

# TeamCity
_TeamCity/

# Global JSON
global.json

# Node Modules
node_modules/
.yarn/
.pnp.*

# OS Generated Files
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Temporary Files
~$*
*.tmp
*.temp

# Visual Studio Profiler
*.vsp
*.vspx
*.sap

# Visual Studio Trace Files
*.tss

# Visual Studio Trace Files
*.tss

# Installer Files
*.msi
*.msix
*.appx

# Visual Studio Code Workspace
*.code-workspace

# Result files
.sarif/
*.csv
*.xlsx
**/logs/
**/results/

# End of .gitignore
