/* Main Styles for EndpointInfo Explorer with Bootstrap Integration */

:root {
  --color-primary: #3498db;
  --color-secondary: #2c3e50;
  --color-background: #f8f9fa;
  --color-text: #333;
  --color-light: #f1f1f1;
  --color-border: #ddd;
  --color-added: #28a745;
  --color-removed: #dc3545;
  --color-modified: #ffc107;
  --color-hover: #e9ecef;
  --shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  --border-radius: 4px;
  --transition: all 0.3s ease;
  --font-size-multiplier: 1;
  --top-menu-height: 60px;
  --left-menu-width: 250px;
  --left-menu-collapsed-width: 0px;
  --detail-view-width: 40%;
  --detail-view-min-width: 300px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: var(--color-background);
  color: var(--color-text);
  line-height: 1.6;
  height: 100vh;
  overflow: hidden;
}

/* Prevent text selection during resize operations */
body.no-select {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* App Container Layout */
.app-container {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

/* App Container with sidebar collapsed */
.app-container.sidebar-collapsed .main-area {
  flex: 1 !important;
  margin-left: 0 !important;
}

/* Ensure detail view is always properly positioned */
.detail-view.active {
  display: block;
  transform: translateX(0);
  right: 0 !important;
}

/* Left Menu */
.left-menu {
  width: var(--left-menu-width);
  min-width: 200px;
  max-width: 500px;
  background-color: var(--color-light);
  padding: 1rem;
  border-right: 1px solid var(--color-border);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: relative;
  will-change: transform; /* Optimize scrolling performance */
  transition: width 0.3s ease, transform 0.3s ease;
  height: 100%;
  flex-shrink: 0;
  z-index: 1020;
}

.left-menu.collapsed {
  width: 0;
  min-width: 0;
  padding: 0;
  overflow: hidden;
  transform: translateX(-100%);
}

/* Main Area (contains top menu and content) */
.main-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: width 0.3s ease, margin-left 0.3s ease;
}

/* Top Menu */
.top-menu {
  background-color: var(--color-primary);
  color: white;
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow);
  height: var(--top-menu-height);
  z-index: 1010;
  width: 100%;
}

.top-menu h1 {
  font-size: 1.5rem;
  margin: 0 0 0 1rem;
}

.top-menu-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.burger-menu-toggle {
  background-color: transparent;
  color: white;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1030;
  transition: transform 0.3s ease;
}

.burger-menu-toggle:hover {
  transform: scale(1.1);
}

.font-controls {
  display: flex;
  margin-right: 0.5rem;
}

.view-controls {
  display: flex;
  margin-right: 0.5rem;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
}

/* Grid */
.grid-container {
  flex: 1;
  overflow: auto;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  background-color: white;
}

.grid {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed; /* Prevents column redistribution */
}

.grid th {
  background-color: var(--color-secondary);
  color: white;
  padding: 0.75rem;
  text-align: left;
  position: sticky; /* Keep headers visible */
  top: 0;
  z-index: 10;
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
  position: relative; /* For resizer positioning */
}

/* Default Column Widths */
.grid th:nth-child(1) {
  width: 8%;
  min-width: 60px;
  max-width: 100px;
} /* Diff Type */
.grid th:nth-child(2) {
  width: 8%;
  min-width: 120px;
  max-width: 180px;
} /* HTTP Methods */
.grid th:nth-child(3) {
  width: 20%;
  min-width: 120px;
  max-width: 1920px;
} /* Route */
.grid th:nth-child(4) {
  width: 10%;
  min-width: 120px;
  max-width: 1920px;
} /* Policy */
.grid th:nth-child(5) {
  width: 12%;
  min-width: 120px;
  max-width: 200px;
} /* Security Status */
.grid th:nth-child(6) {
  width: 10%;
  min-width: 120px;
  max-width: 200px;
} /* Review Date */
.grid th:nth-child(7) {
  width: 7%;
  min-width: 100px;
  max-width: 150px;
} /* Actions */
.grid th:nth-child(8) {
  width: 25%;
  min-width: 120px;
  max-width: 1920px;
} /* Request Parameters */

.grid th .column-resizer {
  position: absolute;
  top: 0;
  right: -3px; /* Center the 6px resizer over the border */
  width: 6px;
  height: 100%;
  cursor: col-resize;
  background: transparent;
  z-index: 11; /* Above header content */
  /* For debugging visibility: background-color: rgba(255,0,0,0.2); */
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE/Edge */
  user-select: none; /* Non-prefixed last */
}

.grid th:hover .column-resizer {
  background-color: rgba(52, 152, 219, 0.5); /* Light blue on hover */
  opacity: 0.7;
}

.grid th .column-resizer.active {
  background-color: var(--color-primary);
  opacity: 1;
  box-shadow: 0 0 6px rgba(52, 152, 219, 0.6);
}

.grid th::after {
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 8px;
  vertical-align: middle;
}

.grid th.sort-asc::after {
  content: "▲";
  font-size: 0.7em;
}

.grid th.sort-desc::after {
  content: "▼";
  font-size: 0.7em;
}

.grid td {
  padding: 0.75rem;
  border-bottom: 1px solid var(--color-border);
  transition: padding 0.3s ease;
  overflow: hidden; /* Required for text-overflow */
  text-overflow: ellipsis; /* Default overflow handling */
  white-space: nowrap; /* Default overflow handling */
  max-width: 0; /* Important for ellipsis to work with table-layout:fixed */
}

/* Text Wrap Styles */
.grid.text-wrap td {
  white-space: normal;
  word-wrap: break-word; /* For older browsers */
  overflow-wrap: break-word; /* Standard property */
  text-overflow: clip; /* Turn off ellipsis when wrapping */
  max-width: none; /* Allow content to determine width/height */
  line-height: 1.4; /* Add some spacing between lines */
}

.grid.compact th {
  padding: 0.4rem 0.5rem;
}

.grid.compact td {
  padding: 0.3rem 0.5rem;
  font-size: 0.9em;
}

.grid tr:hover {
  background-color: var(--color-hover);
  cursor: pointer;
}

/* Row types */
.row-added {
  background-color: rgba(40, 167, 69, 0.1);
  border-left: 4px solid var(--color-added);
}

.row-deleted {
  background-color: rgba(220, 53, 69, 0.1);
  border-left: 4px solid var(--color-removed);
}

.row-removed {
  background-color: rgba(220, 53, 69, 0.1);
  border-left: 4px solid var(--color-removed);
}

.row-modified {
  background-color: rgba(255, 193, 7, 0.1);
  border-left: 4px solid var(--color-modified);
}

.row-unmodified {
  background-color: rgba(108, 117, 125, 0.05);
  border-left: 4px solid #6c757d;
}

/* Resizer for left menu */
.left-menu-resizer {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 5px;
  background-color: var(--color-border);
  cursor: ew-resize;
  opacity: 0.5;
  transition: opacity 0.3s;
  z-index: 1025;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.left-menu-resizer:hover,
.left-menu-resizer.active {
  opacity: 1;
  background-color: var(--color-primary);
}

/* Filter Styles */
.filter-section {
  margin-bottom: 1rem;
}

.filter-section h3 {
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: var(--color-secondary);
}

.filter-group {
  margin-bottom: 1rem;
}

.filter-group label {
  display: block;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
}

.filter-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
}

.checkbox-group {
  margin-bottom: 0.3rem;
}

.radio-group {
  margin-bottom: 0.3rem;
}

/* Tooltip */
.tooltip {
  position: absolute;
  background-color: #ffffe0; /* Tooltip yellow */
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  padding: 0.75rem; /* Adjusted padding */
  box-shadow: var(--shadow);
  max-width: 500px; /* Increased max-width */
  z-index: 1050;
  display: none;
  pointer-events: none; /* Prevent tooltip from interfering with mouse events */
  font-size: 0.85em; /* Smaller font size */
}

.tooltip ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.tooltip li {
  margin-bottom: 0.25rem;
}

/* Detail View */
.detail-view {
  position: fixed;
  top: 0;
  right: 0;
  width: var(--detail-view-width);
  min-width: var(--detail-view-min-width);
  max-width: 50%;
  height: 100%;
  background-color: white;
  border-left: 1px solid var(--color-border);
  overflow-y: auto;
  padding: 1rem;
  display: none;
  transition: width 0.3s ease, transform 0.3s ease;
  z-index: 1040;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
}

.detail-view.active {
  display: block;
  transform: translateX(0);
}

.detail-view.pinned {
  position: relative;
  transform: none;
  box-shadow: none;
  flex-shrink: 0;
  display: block;
}

/* Resizer for detail view */
.detail-view-resizer {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 5px;
  background-color: var(--color-border);
  cursor: ew-resize;
  opacity: 0.5;
  transition: opacity 0.3s;
  z-index: 1045;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.detail-view-resizer:hover,
.detail-view-resizer.active {
  opacity: 1;
  background-color: var(--color-primary);
}

.detail-header {
  display: flex;
  align-items: center; /* Vertically align items */
  justify-content: space-between; /* Distribute space */
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--color-border);
  margin-bottom: 1rem;
}

.detail-header h2 {
  margin: 0;
  font-size: 1.25rem;
  flex-grow: 1; /* Allow title to take available space */
  text-align: center; /* Center the title */
}

.detail-header .icon-button,
.detail-header .btn {
  /* Style the new close button */
  background-color: transparent;
  border: none;
  font-size: 1.5rem; /* Match burger menu */
  cursor: pointer;
  padding: 0.25rem 0.5rem; /* Match burger menu */
  color: var(--color-secondary); /* Match other icons */
}

.detail-header .icon-button:hover,
.detail-header .btn:hover {
  color: var(--color-primary);
}

.detail-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Add some space between action items */
}

.icon-button {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--color-text);
  padding: 0.25rem;
  border-radius: var(--border-radius);
  transition: background-color 0.3s, color 0.3s, transform 0.2s;
}

.icon-button:hover {
  background-color: var(--color-hover);
  transform: scale(1.1);
}

.icon-button.active {
  color: var(--color-primary);
  background-color: rgba(52, 152, 219, 0.1);
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Property Grid Styles */
.property-grid {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.property-grid th {
  background-color: var(--color-secondary);
  color: white;
  padding: 0.5rem;
  text-align: left;
}

.property-grid td {
  padding: 0.5rem;
  border: 1px solid var(--color-border);
  vertical-align: top;
}

.property-grid .field-name {
  font-weight: bold;
  width: 25%;
  background-color: #f8f9fa;
}

.property-grid .old-value,
.property-grid .new-value {
  width: 37.5%;
}

.property-grid .highlight-change {
  background-color: rgba(255, 193, 7, 0.1);
}

.property-grid .changed-field-row {
  background-color: rgba(255, 193, 7, 0.05);
}

.property-grid .complex-object-header td {
  background-color: #e9ecef;
  font-weight: bold;
  text-align: center;
}

.property-group {
  margin-bottom: 1rem;
}

.property-group h3 {
  margin-bottom: 0.5rem;
  color: var(--color-secondary);
  font-size: 1.1rem;
}

.property-row {
  display: flex;
  margin-bottom: 0.5rem;
}

.property-label {
  width: 150px;
  font-weight: bold;
  color: var(--color-secondary);
}

.property-value {
  flex: 1;
}

/* Comparison View */
.comparison-view {
  display: flex;
  gap: 1rem;
}

.comparison-column {
  flex: 1;
  padding: 1rem;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
}

.comparison-column h3 {
  margin-bottom: 0.5rem;
  text-align: center;
  color: var(--color-secondary);
}

.comparison-column .property-group h3 {
  text-align: left;
}

.old-value {
  background-color: rgba(220, 53, 69, 0.1);
}

.new-value {
  background-color: rgba(40, 167, 69, 0.1);
}

/* Single endpoint view styling for Added/Removed items */
.single-endpoint-view {
  padding: 1rem;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
}

.changed-field {
  background-color: rgba(255, 193, 7, 0.1);
  padding: 0.25rem;
  border-radius: var(--border-radius);
}

/* App Container with pinned layout */
.app-container.pinned-layout {
  display: flex;
}

.app-container.pinned-layout .main-area {
  flex: 1;
  min-width: 0; /* Allow flex item to shrink below content size */
  order: 1; /* Ensure main area is in the middle */
}

.app-container.pinned-layout .left-menu {
  order: 0; /* Left menu comes first */
}

.app-container.pinned-layout .detail-view.pinned {
  flex-shrink: 0;
  position: relative;
  transform: none;
  box-shadow: none;
  display: block;
  order: 2; /* Detail view always comes last (right side) */
}

/* When sidebar is collapsed and detail view is pinned */
.app-container.sidebar-collapsed.pinned-layout .main-area {
  position: relative !important; /* Override the absolute positioning */
  left: auto !important;
  right: auto !important;
  margin-left: 0 !important;
  flex: 1 !important;
  order: 1;
}

.app-container.sidebar-collapsed.pinned-layout .detail-view.pinned {
  order: 2; /* Keep detail view on the right */
}

/* Responsive */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }

  .left-menu {
    width: 100%;
    max-width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--color-border);
    max-height: 200px;
  }

  .detail-view {
    width: 100%;
    max-width: 100%;
  }

  .top-menu {
    flex-wrap: wrap;
  }

  .top-menu-actions {
    margin-top: 0.5rem;
    width: 100%;
    justify-content: space-between;
  }
}

/* Hidden file input */
#file-input {
  display: none;
}
/* Security Review Styles */
.security-status {
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  display: inline-block;
  text-align: center;
  min-width: 100px;
}

.security-status-compliant {
  background-color: rgba(40, 167, 69, 0.2);
  color: #155724;
}

.security-status-non-compliant {
  background-color: rgba(220, 53, 69, 0.2);
  color: #721c24;
}

.security-status-risk-accepted {
  background-color: rgba(255, 193, 7, 0.2);
  color: #856404;
}

.security-status-under-review {
  background-color: rgba(23, 162, 184, 0.2);
  color: #0c5460;
}

.security-status-critical-vulnerability {
  background-color: rgba(220, 53, 69, 0.4);
  color: #721c24;
  font-weight: 700;
}

.security-status-must-review {
  background-color: #ff0000 !important;
  color: #ffffff !important;
  font-weight: bold !important;
}

.security-status-none {
  background-color: rgba(108, 117, 125, 0.2);
  color: #495057;
  font-style: italic;
}

/* Security Review Action Buttons */
.security-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.security-action-btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  border: 1px solid var(--color-border);
  background-color: white;
  transition: all 0.2s ease;
}

.security-action-btn:hover {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.add-review-btn {
  color: #155724;
  border-color: #28a745;
}

.view-history-btn {
  color: #0c5460;
  border-color: #17a2b8;
}

/* Security Review History Modal Styles */
#securityReviewHistoryModal .modal-dialog {
  max-width: 800px;
}

#securityReviewHistoryModal .security-status {
  width: 100%;
  display: inline-block;
  text-align: center;
}

#review-history-table {
  margin-bottom: 0;
}

.sort-btn {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
}

.sort-btn.active {
  font-weight: bold;
}

/* Security Review History Specific Styles */
.review-notes-container {
  position: relative;
}

.review-notes-preview {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.review-notes-content {
  white-space: pre-wrap;
  background-color: #f8f9fa;
  padding: 0.5rem;
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
  border-left: 3px solid #dee2e6;
  max-height: 300px;
  overflow-y: auto;
}

.review-notes-toggle {
  padding: 0;
  font-size: 0.8rem;
  color: var(--color-primary);
  text-decoration: none;
}

.review-notes-toggle:hover {
  text-decoration: underline;
}

/* Improve table header styling for better clickability */
#review-history-table th {
  cursor: pointer;
  position: relative;
}

#review-history-table th:hover {
  background-color: #212529;
}

/* Add a subtle indicator on hover */
#review-history-table th:hover::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--color-primary);
}

/* Make the security review history modal wider */
#securityReviewHistoryModal .modal-dialog {
  max-width: 800px;
}

/* Ensure the modal is properly centered */
#securityReviewHistoryModal .modal-dialog {
  margin-left: auto;
  margin-right: auto;
  color: #0c5460;
  border-color: #17a2b8;
}

/* Security Review History Table Styles */
/* Fix for table header hover turning black - override Bootstrap's table-hover for headers */
#review-history-table.table-hover thead th:hover {
  background-color: inherit !important;
  color: inherit !important;
}

/* Ensure table headers maintain their original styling on hover */
#review-history-table thead th {
  background-color: var(--bs-gray-100, #f8f9fa);
  color: var(--bs-dark, #212529);
  border-bottom: 2px solid var(--bs-gray-300, #dee2e6);
}

#review-history-table thead th:hover {
  background-color: var(--bs-gray-100, #f8f9fa) !important;
  color: var(--bs-dark, #212529) !important;
  cursor: pointer;
}

/* Optional: Add subtle hover effect for sortable headers */
#review-history-table thead th:hover {
  background-color: var(--bs-gray-200, #e9ecef) !important;
  transition: background-color 0.15s ease-in-out;
}
/* Advanced Security Search Modal Styles */
#advancedSecuritySearchModal .modal-dialog {
  max-width: 90%;
  width: 90%;
}

#advancedSecuritySearchModal .card {
  border: 1px solid #dee2e6;
}

#advancedSecuritySearchModal .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

/* Search form styling */
#advancedSearchForm .form-label {
  font-weight: 500;
  color: var(--color-secondary);
}

#advancedSearchForm .form-text {
  font-size: 0.875rem;
  color: #6c757d;
}

/* Security status checkboxes in search form */
#advancedSearchForm .form-check {
  margin-bottom: 0.5rem;
}

#advancedSearchForm .form-check-label {
  display: flex;
  align-items: center;
  width: 100%;
}

#advancedSearchForm .security-status {
  margin-left: 0.5rem;
  font-size: 0.875rem;
}

/* Search results table styling */
#search-results-table {
  font-size: 0.9rem;
}

#search-results-table th {
  background-color: var(--color-secondary);
  color: white;
  font-weight: 500;
  border-bottom: 2px solid #dee2e6;
  position: sticky;
  top: 0;
  z-index: 10;
}

#search-results-table th:hover {
  background-color: #1a252f;
  cursor: pointer;
}

#search-results-table tbody tr {
  transition: background-color 0.15s ease-in-out;
}

#search-results-table tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.1);
  cursor: pointer;
}

/* Results count badge */
#results-count {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Sort buttons for results */
.results-sort-btn {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
}

.results-sort-btn.active {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

/* Export button styling */
#export-results:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Advanced search button in top menu */
#advanced-search-btn {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: white;
}

#advanced-search-btn:hover {
  background-color: #138496;
  border-color: #117a8b;
}

/* Responsive adjustments for advanced search modal */
@media (max-width: 768px) {
  #advancedSecuritySearchModal .modal-dialog {
    max-width: 95%;
    width: 95%;
    margin: 0.5rem;
  }
  
  #advancedSecuritySearchModal .card-body .row {
    margin-bottom: 1rem;
  }
  
  #search-results-table {
    font-size: 0.8rem;
  }
  
  #search-results-table th,
  #search-results-table td {
    padding: 0.5rem 0.25rem;
  }
}

/* Sticky table header for search results */
.table-responsive {
  position: relative;
}

.table-responsive .table thead th {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Enhanced Loading States and Error Handling */
.search-loading {
  opacity: 0.6;
  pointer-events: none;
}

.search-loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Loading state for buttons */
.loading-state {
  position: relative;
  pointer-events: none;
}

.loading-state .spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.1em;
}

/* Loading overlay */
.loading-overlay {
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.loading-overlay .spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Toast container positioning */
.toast-container {
  max-width: 400px;
}

.toast-container .toast {
  margin-bottom: 0.5rem;
}

/* Enhanced toast styling */
.toast .toast-header {
  font-weight: 600;
}

.toast .toast-body {
  word-wrap: break-word;
}

.toast .btn-sm {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}

/* Error state styling */
.alert details {
  margin-top: 0.5rem;
}

.alert details summary {
  cursor: pointer;
  font-size: 0.875rem;
}

.alert details summary:hover {
  text-decoration: underline;
}

.alert .btn-sm {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  margin-top: 0.5rem;
}

/* Form validation enhancements */
.was-validated .form-control:invalid,
.was-validated .form-select:invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.was-validated .form-control:valid,
.was-validated .form-select:valid {
  border-color: #198754;
  box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.invalid-feedback {
  display: block;
  font-size: 0.875rem;
  color: #dc3545;
  margin-top: 0.25rem;
}

.valid-feedback {
  display: block;
  font-size: 0.875rem;
  color: #198754;
  margin-top: 0.25rem;
}

/* Progress indicators */
.progress-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--color-text-muted);
}

.progress-indicator .spinner-border {
  width: 1rem;
  height: 1rem;
  border-width: 0.1em;
}

/* Retry button styling */
.retry-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.retry-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.status-indicator.success {
  background-color: rgba(25, 135, 84, 0.1);
  color: #198754;
}

.status-indicator.error {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.status-indicator.warning {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.status-indicator.info {
  background-color: rgba(13, 202, 240, 0.1);
  color: #0dcaf0;
}

/* Animation for spin */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Fade in animation for toasts */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.toast {
  animation: fadeIn 0.3s ease-out;
}

/* Accessibility improvements */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Focus management for error states */
.alert:focus,
.toast:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Responsive adjustments for error handling */
@media (max-width: 768px) {
  .toast-container {
    max-width: calc(100vw - 2rem);
    left: 1rem !important;
    right: 1rem !important;
  }
  
  .loading-overlay .spinner-border {
    width: 2rem;
    height: 2rem;
  }
  
  .alert details {
    font-size: 0.875rem;
  }
}