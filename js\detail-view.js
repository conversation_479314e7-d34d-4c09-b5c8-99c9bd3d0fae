// Detail View Management Module
// Handles the detail view panel showing endpoint information

// Show detail view with endpoint data
function showDetailView(endpoint) {
  AppState.setCurrentEndpoint(endpoint);
  window.currentEndpoint = endpoint; // For backward compatibility
  
  const detailView = document.getElementById("detail-view");
  const detailTitle = document.getElementById("detail-title");
  const detailContent = document.getElementById("detail-content");

  // Set title based on diffType
  const titleEndpoint =
    endpoint.diffType === "Removed"
      ? endpoint.oldValue
      : endpoint.newValue || endpoint.oldValue;
  detailTitle.textContent = `${endpoint.diffType}: ${titleEndpoint.route}`;

  // Clear previous content
  detailContent.innerHTML = "";

  // Build detail content based on grid view toggle state
  const gridViewToggle = document.getElementById("grid-view-toggle");
  const isGridViewEnabled = gridViewToggle && gridViewToggle.checked;
  if (isGridViewEnabled) {
    // Use grid view for all diff types
    renderGridViewDetails(endpoint, detailContent);
  } else {
    // Use standard view based on diff type
    if (endpoint.diffType === "Modified") {
      renderStandardViewDetails(endpoint, detailContent);
    } else {
      // For Added and Removed endpoints, create a wrapper with appropriate styling
      const wrapper = document.createElement("div");
      wrapper.className = "single-endpoint-view";

      // Apply appropriate background color based on diff type
      if (endpoint.diffType === "Added") {
        wrapper.classList.add("new-value");
        // Pass the actual endpoint data (newValue) to renderEndpointDetails
        renderEndpointDetails(endpoint.newValue, wrapper);
      } else if (endpoint.diffType === "Removed") {
        wrapper.classList.add("old-value");
        // Pass the actual endpoint data (oldValue) to renderEndpointDetails
        renderEndpointDetails(endpoint.oldValue, wrapper);
      } else {
        // For any other types (like Unmodified), use the appropriate value
        const actualEndpoint =
          endpoint.diffType === "Removed"
            ? endpoint.oldValue
            : endpoint.newValue || endpoint.oldValue;
        renderEndpointDetails(actualEndpoint, wrapper);
      }

      detailContent.appendChild(wrapper);
    }
  }

  // Show the detail view if not already pinned
  if (!AppState.isDetailPinned()) {
    detailView.classList.add("active");

    // Ensure proper positioning for non-pinned view
    detailView.style.position = "fixed";
    detailView.style.top = "0";
    detailView.style.right = "0";
  }

  // Ensure pin button icon and close button visibility are correct
  if (window.LayoutManager) {
    window.LayoutManager.updatePinButtonIcon();
  }

  // Update main area width to account for detail view
  setTimeout(() => {
    if (window.LayoutManager) {
      window.LayoutManager.updateMainAreaWidth();
    }
  }, 0);
}

// Render endpoint details in a container
function renderEndpointDetails(endpoint, container, skipSecurityReview = false) {
  // Basic Info
  const basicInfo = document.createElement("div");
  basicInfo.className = "property-group";
  const httpMethodsDisplay = endpoint.httpMethods
    ? endpoint.httpMethods.join(", ")
    : "";
  basicInfo.innerHTML = `
        <h3>Basic Information</h3>
        <div class="property-row">
            <div class="property-label">ID</div>
            <div class="property-value">${endpoint.id}</div>
        </div>
        <div class="property-row">
            <div class="property-label">Project Name</div>
            <div class="property-value">${endpoint.projectName || ""}</div>
        </div>
        <div class="property-row">
            <div class="property-label">Namespace</div>
            <div class="property-value">${endpoint.namespace || ""}</div>
        </div>
        <div class="property-row">
            <div class="property-label">Controller</div>
            <div class="property-value">${endpoint.controller || ""}</div>
        </div>
        <div class="property-row">
            <div class="property-label">Action Name</div>
            <div class="property-value">${endpoint.actionName}</div>
        </div>
        <div class="property-row">
            <div class="property-label">Route</div>
            <div class="property-value">${endpoint.route}</div>
        </div>
        <div class="property-row">
            <div class="property-label">HTTP Methods</div>
            <div class="property-value">${httpMethodsDisplay}</div>
        </div>
        <div class="property-row">
            <div class="property-label">Policy</div>
            <div class="property-value">${endpoint.policy || ""}</div>
        </div>
        <div class="property-row">
            <div class="property-label">Exclude From API Client</div>
            <div class="property-value">${
              endpoint.excludeFromApiClient === true ? "true" : "false"
            }</div>
        </div>
        <div class="property-row">
            <div class="property-label">Description</div>
            <div class="property-value">${endpoint.description || ""}</div>
        </div>
        <div class="property-row">
            <div class="property-label">Response</div>
            <div class="property-value">${formatValueForDisplay(
              endpoint.response || ""
            )}</div>
        </div>
        <div class="property-row">
            <div class="property-label">File Name</div>
            <div class="property-value">${endpoint.fileName || ""}</div>
        </div>
        <div class="property-row">
            <div class="property-label">Line Number</div>
            <div class="property-value">${endpoint.lineNumber || ""}</div>
        </div>
        <div class="property-row">
            <div class="property-label">CRC64</div>
            <div class="property-value">${endpoint.crC64 || ""}</div>
        </div>
    `;
  container.appendChild(basicInfo);

  // Security Review Information (only if not skipped)
  if (!skipSecurityReview) {
    renderSecurityReviewSection(endpoint, container);
  }

  // Request Parameters
  if (endpoint.requestParameters && endpoint.requestParameters.length > 0) {
    const paramsSection = document.createElement("div");
    paramsSection.className = "property-group";
    paramsSection.innerHTML = `<h3>Request Parameters</h3>`;

    endpoint.requestParameters.forEach((param) => {
      const paramRow = document.createElement("div");
      paramRow.className = "property-row";
      paramRow.innerHTML = `
                <div class="property-label">Parameter</div>
                <div class="property-value">${formatValueForDisplay(
                  param
                )}</div>
            `;
      paramsSection.appendChild(paramRow);
    });

    container.appendChild(paramsSection);
  }

  // Other Attributes
  if (endpoint.otherAttributes && endpoint.otherAttributes.length > 0) {
    const attrsSection = document.createElement("div");
    attrsSection.className = "property-group";
    attrsSection.innerHTML = `<h3>Other Attributes</h3>`;

    endpoint.otherAttributes.forEach((attr) => {
      const attrRow = document.createElement("div");
      attrRow.className = "property-row";
      attrRow.innerHTML = `
                <div class="property-label">Attribute</div>
                <div class="property-value">${formatValueForDisplay(attr)}</div>
            `;
      attrsSection.appendChild(attrRow);
    });

    container.appendChild(attrsSection);
  }

  // HTTP Status Codes
  if (endpoint.httpStatusCodes && endpoint.httpStatusCodes.length > 0) {
    const codesSection = document.createElement("div");
    codesSection.className = "property-group";
    codesSection.innerHTML = `<h3>HTTP Status Codes</h3>`;

    endpoint.httpStatusCodes.forEach((code) => {
      const codeRow = document.createElement("div");
      codeRow.className = "property-row";
      codeRow.innerHTML = `
                <div class="property-label">Status Code</div>
                <div class="property-value">${formatValueForDisplay(code)}</div>
            `;
      codesSection.appendChild(codeRow);
    });

    container.appendChild(codesSection);
  }
}

// Render security review section
function renderSecurityReviewSection(endpoint, container) {
  // Get the latest security review for this endpoint
  const latestReview = window.getLatestSecurityReview ? window.getLatestSecurityReview(endpoint.id) : null;
  const allReviews = window.getSecurityReviewsForEndpoint ? window.getSecurityReviewsForEndpoint(endpoint.id) : [];

  // Create security review section
  const securitySection = document.createElement("div");
  securitySection.className = "property-group";

  const sectionHeader = document.createElement("h3");
  sectionHeader.textContent = "Security Review";
  securitySection.appendChild(sectionHeader);

  if (latestReview) {
    // Display latest security review information
    const statusRow = document.createElement("div");
    statusRow.className = "property-row";
    statusRow.innerHTML = `
      <div class="property-label">Security Status</div>
      <div class="property-value">
        <span class="security-status security-status-${latestReview.securityStatus
          .toLowerCase()
          .replace(/\s+/g, "-")}">${latestReview.securityStatus}</span>
      </div>
    `;
    securitySection.appendChild(statusRow);

    const reviewDateRow = document.createElement("div");
    reviewDateRow.className = "property-row";
    reviewDateRow.innerHTML = `
      <div class="property-label">Last Review Date</div>
      <div class="property-value">${window.formatReviewDate ? window.formatReviewDate(latestReview.reviewDateTime) : latestReview.reviewDateTime}</div>
    `;
    securitySection.appendChild(reviewDateRow);

    const reviewerRow = document.createElement("div");
    reviewerRow.className = "property-row";
    reviewerRow.innerHTML = `
      <div class="property-label">Last Reviewer</div>
      <div class="property-value">${latestReview.reviewerUsername}</div>
    `;
    securitySection.appendChild(reviewerRow);

    if (latestReview.reviewNotes && latestReview.reviewNotes.trim()) {
      const notesRow = document.createElement("div");
      notesRow.className = "property-row";
      notesRow.innerHTML = `
        <div class="property-label">Latest Notes</div>
        <div class="property-value">${formatValueForDisplay(
          latestReview.reviewNotes
        )}</div>
      `;
      securitySection.appendChild(notesRow);
    }

    // Add review count information
    const countRow = document.createElement("div");
    countRow.className = "property-row";
    countRow.innerHTML = `
      <div class="property-label">Total Reviews</div>
      <div class="property-value">${allReviews.length}</div>
    `;
    securitySection.appendChild(countRow);
  } else {
    // No security reviews exist
    const noReviewRow = document.createElement("div");
    noReviewRow.className = "property-row";
    noReviewRow.innerHTML = `
      <div class="property-label">Security Status</div>
      <div class="property-value">
        <span class="security-status security-status-none">No Review</span>
      </div>
    `;
    securitySection.appendChild(noReviewRow);
  }

  // Add action buttons
  const actionsRow = document.createElement("div");
  actionsRow.className = "property-row";

  const actionsDiv = document.createElement("div");
  actionsDiv.innerHTML = `
    <div class="property-label">Actions</div>
    <div class="property-value">
      <div class="security-actions">
      </div>
    </div>
  `;

  const actionsContainer = actionsDiv.querySelector(".security-actions");

  // Add Review button
  const addReviewBtn = document.createElement("button");
  addReviewBtn.className = "security-action-btn add-review-btn";
  addReviewBtn.textContent = "Add Review";

  // Add data attributes for the security-review-fix.js handler
  addReviewBtn.setAttribute("data-endpoint-id", endpoint.id);
  addReviewBtn.setAttribute("data-route", endpoint.route);
  addReviewBtn.setAttribute(
    "data-http-method",
    (endpoint.httpMethods || []).join(", ")
  );

  actionsContainer.appendChild(addReviewBtn);

  // View Full History button (only if reviews exist)
  if (allReviews.length > 0) {
    const viewHistoryBtn = document.createElement("button");
    viewHistoryBtn.className = "security-action-btn view-history-btn";
    viewHistoryBtn.textContent = "View Full History";

    // Add data attributes for the security-review-fix.js handler
    viewHistoryBtn.setAttribute("data-endpoint-id", endpoint.id);
    viewHistoryBtn.setAttribute("data-route", endpoint.route);
    viewHistoryBtn.setAttribute(
      "data-http-method",
      (endpoint.httpMethods || []).join(", ")
    );

    actionsContainer.appendChild(viewHistoryBtn);
  }

  securitySection.appendChild(actionsDiv);

  container.appendChild(securitySection);
}

// Render modified endpoint details
function renderModifiedEndpointDetails(item, container) {
  // Check if grid view is enabled
  const gridViewToggle = document.getElementById("grid-view-toggle");
  const isGridViewEnabled = gridViewToggle && gridViewToggle.checked;

  if (isGridViewEnabled) {
    // Render in grid view mode
    renderGridViewDetails(item, container);
  } else {
    // Render in standard view mode
    renderStandardViewDetails(item, container);
  }
}

// Render grid view details
function renderGridViewDetails(item, container) {
  // Create grid table
  const gridTable = document.createElement("table");
  gridTable.className = "table table-bordered property-grid";
  // Determine which columns to show based on diffType
  const showOldValue = item.diffType !== "Added";
  const showNewValue = item.diffType !== "Removed";

  // Create header row with appropriate columns
  const headerRow = document.createElement("thead");
  let headerHTML = "<tr><th>Field Name</th>";

  if (showOldValue) {
    headerHTML += "<th>Old Value</th>";
  }

  if (showNewValue) {
    headerHTML += "<th>New Value</th>";
  }

  headerHTML += "</tr>";
  headerRow.innerHTML = headerHTML;
  gridTable.appendChild(headerRow);

  // Create table body
  const tableBody = document.createElement("tbody");
  // Get all fields from both old and new values
  const oldValue = item.oldValue || {};
  let newValue = item.newValue || {};

  // For Unmodified endpoints, if there's no newValue, use oldValue for both columns
  if (item.diffType === "Unmodified" && Object.keys(newValue).length === 0) {
    newValue = oldValue;
  }

  // Combine all field names from both objects
  const allFields = new Set([
    ...Object.keys(oldValue || {}),
    ...Object.keys(newValue || {}),
  ]);

  // Add basic fields first in a specific order
  const orderedBasicFields = [
    "id",
    "projectName",
    "namespace",
    "controller",
    "actionName",
    "route",
    "policy",
    "httpMethods",
    "consumes",
    "description",
    "httpStatusCodes",
    "otherAttributes",
    "excludeFromApiClient",
    "response",
    "requestParameters",
    "apiVersion",
    "fileName",
    "lineNumber",
    "crC64",
  ];
  // Process ordered fields first
  orderedBasicFields.forEach((fieldName) => {
    // Process all ordered fields, even if they don't exist in the data
    // This ensures fields like excludeFromApiClient with false values are shown
    const oldFieldValue = oldValue[fieldName];
    const newFieldValue = newValue[fieldName];

    // Check if this field has changed
    // Note: fieldDiffs uses different field name conventions
    const fieldNameMappings = {
      response: "Response",
      crC64: "CRC64",
      requestParameters: "RequestParameters",
      httpMethods: "HttpMethods",
      httpStatusCodes: "HttpStatusCodes",
      otherAttributes: "OtherAttributes",
      projectName: "ProjectName",
      namespace: "Namespace",
      policy: "Policy",
      excludeFromApiClient: "ExcludeFromApiClient",
    };
    const mappedFieldName =
      fieldNameMappings[fieldName] ||
      fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
    const isChanged = item.fieldDiffs && item.fieldDiffs[mappedFieldName]; // Create row with appropriate styling
    const row = document.createElement("tr");
    if (isChanged) {
      row.className = "changed-field-row";
    }

    // Format values for display
    const oldDisplayValue = formatValueForDisplay(oldFieldValue, fieldName);
    const newDisplayValue = formatValueForDisplay(newFieldValue, fieldName);

    // Build row HTML based on which columns to show
    let rowHTML = `<td class="field-name">${fieldName}</td>`;

    if (showOldValue) {
      rowHTML += `<td class="old-value ${
        isChanged ? "highlight-change" : ""
      }">${oldDisplayValue}</td>`;
    }

    if (showNewValue) {
      rowHTML += `<td class="new-value ${
        isChanged ? "highlight-change" : ""
      }">${newDisplayValue}</td>`;
    }

    row.innerHTML = rowHTML;

    tableBody.appendChild(row);
    allFields.delete(fieldName);
  });

  // Process remaining fields
  allFields.forEach((fieldName) => {
    // Skip complex objects that need special handling (these don't exist in the new format)
    if (["InputParameters", "Attributes", "ReturnCodes"].includes(fieldName)) {
      return;
    }

    const oldFieldValue = oldValue[fieldName];
    const newFieldValue = newValue[fieldName];

    // Check if this field has changed
    // Note: fieldDiffs uses different field name conventions
    const fieldNameMappings = {
      response: "Response",
      crC64: "CRC64",
      requestParameters: "RequestParameters",
      httpMethods: "HttpMethods",
      httpStatusCodes: "HttpStatusCodes",
      otherAttributes: "OtherAttributes",
      projectName: "ProjectName",
      namespace: "Namespace",
      policy: "Policy",
    };
    const mappedFieldName =
      fieldNameMappings[fieldName] ||
      fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
    const isChanged = item.fieldDiffs && item.fieldDiffs[mappedFieldName]; // Create row with appropriate styling
    const row = document.createElement("tr");
    if (isChanged) {
      row.className = "changed-field-row";
    }

    // Format values for display
    const oldDisplayValue = formatValueForDisplay(oldFieldValue, fieldName);
    const newDisplayValue = formatValueForDisplay(newFieldValue, fieldName);

    // Build row HTML based on which columns to show
    let rowHTML = `<td class="field-name">${fieldName}</td>`;

    if (showOldValue) {
      rowHTML += `<td class="old-value ${
        isChanged ? "highlight-change" : ""
      }">${oldDisplayValue}</td>`;
    }

    if (showNewValue) {
      rowHTML += `<td class="new-value ${
        isChanged ? "highlight-change" : ""
      }">${newDisplayValue}</td>`;
    }

    row.innerHTML = rowHTML;

    tableBody.appendChild(row);
  });

  gridTable.appendChild(tableBody);
  container.appendChild(gridTable);

  // Add security review information for grid view
  const actualEndpoint =
    item.diffType === "Removed"
      ? item.oldValue
      : item.newValue || item.oldValue;
  if (actualEndpoint && actualEndpoint.id) {
    renderSecurityReviewSection(actualEndpoint, container);
  }
}

// Render standard view details
function renderStandardViewDetails(item, container) {
  // Create comparison view
  const comparisonView = document.createElement("div");
  comparisonView.className = "comparison-view";

  // Old value column
  const oldColumn = document.createElement("div");
  oldColumn.className = "comparison-column old-value";
  oldColumn.innerHTML = `<h3>Old Value</h3>`;

  // New value column
  const newColumn = document.createElement("div");
  newColumn.className = "comparison-column new-value";
  newColumn.innerHTML = `<h3>New Value</h3>`;

  // Add columns to comparison view
  comparisonView.appendChild(oldColumn);
  comparisonView.appendChild(newColumn);

  // Add comparison view to container
  container.appendChild(comparisonView);

  // Render details in each column (skip security reviews to avoid duplication)
  renderEndpointDetails(item.oldValue, oldColumn, true);
  renderEndpointDetails(item.newValue, newColumn, true);

  // Add field diffs section
  if (item.fieldDiffs && item.fieldDiffs.length > 0) {
    const diffSection = document.createElement("div");
    diffSection.className = "property-group";
    diffSection.innerHTML = `<h3>Changed Fields</h3>`;

    item.fieldDiffs.forEach((diff) => {
      const diffRow = document.createElement("div");
      diffRow.className = "property-row";

      // For complex objects like arrays, we'll show a simplified view
      const oldValue =
        typeof diff.oldValue === "string"
          ? diff.oldValue
          : JSON.stringify(diff.oldValue);
      const newValue =
        typeof diff.newValue === "string"
          ? diff.newValue
          : JSON.stringify(diff.newValue);

      diffRow.innerHTML = `
                <div class="property-label">${diff.fieldName}</div>
                <div class="property-value">
                    <div class="changed-field">
                        <strong>Old:</strong> ${oldValue}<br>
                        <strong>New:</strong> ${newValue}
                    </div>
                </div>
            `;
      diffSection.appendChild(diffRow);
    });

    container.appendChild(diffSection);
  }

  // Add security review information for standard view (only once, not for both old and new values)
  const actualEndpoint = item.newValue || item.oldValue;
  if (actualEndpoint && actualEndpoint.id) {
    renderSecurityReviewSection(actualEndpoint, container);
  }
}

// Add complex objects to grid (legacy function for compatibility)
function addComplexObjectsToGrid(tableBody, fieldName, oldValue, newValue, fieldDiffs) {
  const oldArray = oldValue && oldValue[fieldName] ? oldValue[fieldName] : [];
  const newArray = newValue && newValue[fieldName] ? newValue[fieldName] : [];

  // Check if this field has changed
  const isChanged =
    fieldDiffs && fieldDiffs.some((diff) => diff.FieldName === fieldName);

  // Determine which columns to show based on DiffType
  const showOldValue = oldValue && Object.keys(oldValue).length > 0;
  const showNewValue = newValue && Object.keys(newValue).length > 0;

  // Add header row for this complex object
  const headerRow = document.createElement("tr");

  headerRow.className = "complex-object-header";

  let headerHTML = `<td colspan="${
    1 + (showOldValue ? 1 : 0) + (showNewValue ? 1 : 0)
  }" class="complex-object-name ${isChanged ? "highlight-change" : ""}">`;
  headerHTML += `<strong>${fieldName}</strong></td>`;
  headerRow.innerHTML = headerHTML;

  tableBody.appendChild(headerRow);

  // If both arrays are empty, show a placeholder row
  if (oldArray.length === 0 && newArray.length === 0) {
    const emptyRow = document.createElement("tr");

    let emptyHTML = `<td class="field-name">-</td>`;
    if (showOldValue) {
      emptyHTML += `<td class="old-value">No items</td>`;
    }
    if (showNewValue) {
      emptyHTML += `<td class="new-value">No items</td>`;
    }

    emptyRow.innerHTML = emptyHTML;
    tableBody.appendChild(emptyRow);
    return;
  }

  // Create a map of items by some identifier
  const itemMap = new Map();

  // Add old items to map
  oldArray.forEach((item) => {
    const key = item.Name || item.Code || JSON.stringify(item);
    itemMap.set(key, { old: item, new: null });
  });

  // Update map with new items
  newArray.forEach((item) => {
    const key = item.Name || item.Code || JSON.stringify(item);
    if (itemMap.has(key)) {
      itemMap.get(key).new = item;
    } else {
      itemMap.set(key, { old: null, new: item });
    }
  });

  // Add rows for each item
  itemMap.forEach((value, key) => {
    const oldItem = value.old;
    const newItem = value.new;
    const itemChanged =
      (oldItem && !newItem) ||
      (!oldItem && newItem) ||
      (oldItem &&
        newItem &&
        JSON.stringify(oldItem) !== JSON.stringify(newItem));

    const row = document.createElement("tr");
    if (itemChanged) {
      row.className = "changed-field-row";
    }

    const oldDisplayValue = oldItem
      ? formatComplexItem(oldItem)
      : "<em>Not present</em>";
    const newDisplayValue = newItem
      ? formatComplexItem(newItem)
      : "<em>Not present</em>";

    let rowHTML = `<td class="field-name">${key}</td>`;

    if (showOldValue) {
      rowHTML += `<td class="old-value ${
        itemChanged ? "highlight-change" : ""
      }">${oldDisplayValue}</td>`;
    }

    if (showNewValue) {
      rowHTML += `<td class="new-value ${
        itemChanged ? "highlight-change" : ""
      }">${newDisplayValue}</td>`;
    }

    row.innerHTML = rowHTML;

    tableBody.appendChild(row);
  });
}

// Format complex item
function formatComplexItem(item) {
  if (!item) return "";

  let result = "";

  // Format based on common properties in complex objects
  if (item.Name !== undefined) {
    result += `<strong>Name:</strong> ${item.Name}<br>`;
  }

  if (item.Type !== undefined) {
    result += `<strong>Type:</strong> ${item.Type}<br>`;
  }

  if (item.IsRequired !== undefined) {
    result += `<strong>Required:</strong> ${
      item.IsRequired ? "Yes" : "No"
    }<br>`;
  }

  if (item.Description !== undefined) {
    result += `<strong>Description:</strong> ${item.Description}<br>`;
  }

  if (item.Code !== undefined) {
    result += `<strong>Code:</strong> ${item.Code}<br>`;
  }

  if (item.Value !== undefined) {
    result += `<strong>Value:</strong> ${item.Value}<br>`;
  }

  // If no specific properties were found, just stringify the object
  if (!result) {
    result = JSON.stringify(item);
  }

  return result;
}

// Close detail view
function closeDetailView() {
  // Only close if not pinned
  if (!AppState.isDetailPinned()) {
    const detailView = document.getElementById("detail-view");
    detailView.classList.remove("active");
    AppState.setCurrentEndpoint(null);
    window.currentEndpoint = null; // For backward compatibility

    // Update main area width after closing detail view
    if (window.LayoutManager) {
      window.LayoutManager.updateMainAreaWidth();
    }
  }
}

// Format value for display (shared with grid manager)
function formatValueForDisplay(value, fieldName = null) {
  // Special handling for boolean fields when value is undefined/null
  if (
    (value === undefined || value === null) &&
    fieldName === "excludeFromApiClient"
  ) {
    return "false";
  }

  if (value === undefined || value === null) {
    return "";
  } else if (typeof value === "boolean") {
    // Explicitly handle boolean values
    return String(value);
  } else if (Array.isArray(value)) {
    // Use the same sanitization for array items
    return value
      .map((item) => {
        if (typeof item === "string") {
          return String(item).replace(
            /[&<>"'`]/g,
            (s) =>
              ({
                "&": "&amp;",
                "<": "&lt;",
                ">": "&gt;",
                '"': "&quot;",
                "'": "&#39;",
                "`": "&#x60;",
              }[s])
          );
        }
        return String(item);
      })
      .join("<br>");
  } else if (typeof value === "object") {
    return JSON.stringify(value);
  } else if (typeof value === "string" && value.match(/^\d{4}-\d{2}-\d{2}T/)) {
    // Format dates
    return new Date(value).toLocaleString();
  } else {
    // Sanitize string values before displaying
    return String(value).replace(
      /[&<>"'`]/g,
      (s) =>
        ({
          "&": "&amp;",
          "<": "&lt;",
          ">": "&gt;",
          '"': "&quot;",
          "'": "&#39;",
          "`": "&#x60;",
        }[s])
    );
  }
}

// Refresh the current detail view if one is open
function refreshDetailView() {
  const currentEndpoint = AppState.getCurrentEndpoint();
  if (currentEndpoint) {
    console.log("Refreshing detail view for endpoint:", currentEndpoint);
    showDetailView(currentEndpoint);
  }
}

// Export functions for use by other modules
window.DetailView = {
  showDetailView,
  renderEndpointDetails,
  renderSecurityReviewSection,
  renderModifiedEndpointDetails,
  renderGridViewDetails,
  renderStandardViewDetails,
  addComplexObjectsToGrid,
  formatComplexItem,
  closeDetailView,
  formatValueForDisplay,
  refreshDetailView
};

// Make showDetailView available globally for backward compatibility
window.showDetailView = showDetailView;
window.refreshDetailView = refreshDetailView;
