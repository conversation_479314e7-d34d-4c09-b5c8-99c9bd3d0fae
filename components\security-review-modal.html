<!-- Security Review Entry Modal -->
<div
  class="modal fade"
  id="securityReviewModal"
  tabindex="-1"
  aria-labelledby="securityReviewModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="securityReviewModalLabel">
          Add Security Review
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <!-- Alert for validation errors -->
        <div
          class="alert alert-danger d-none"
          id="security-review-alert"
          role="alert"
        >
          <strong>Error:</strong>
          <span id="security-review-error-message"></span>
        </div>

        <form id="securityReviewForm" novalidate>
          <input type="hidden" id="security-review-endpoint-id" />

          <!-- Endpoint Information Display -->
          <div class="endpoint-info mb-3 p-2 border rounded bg-light">
            <div class="row">
              <div class="col-md-4">
                <strong>HTTP Method:</strong>
                <span id="security-review-http-method"></span>
              </div>
              <div class="col-md-8">
                <strong>Route:</strong>
                <span id="security-review-route"></span>
              </div>
            </div>
          </div>

          <!-- Review Date/Time -->
          <div class="mb-3">
            <label class="form-label">Review Date/Time</label>
            <div
              class="form-control-plaintext bg-light border rounded p-2"
              id="security-review-datetime-display"
            >
              <!-- Current date/time will be displayed here -->
            </div>
            <input type="hidden" id="security-review-datetime" required />
          </div>

          <!-- Reviewer Username -->
          <div class="mb-3">
            <label for="security-review-username" class="form-label"
              >Reviewer Username <span class="text-danger">*</span></label
            >
            <input
              type="text"
              class="form-control"
              id="security-review-username"
              placeholder="e.g., john.doe"
              required
              pattern="[a-zA-Z0-9._@-]+"
              maxlength="50"
            />
            <div class="invalid-feedback">
              Username is required (1-50 characters, only alphanumeric, .,
              _, @, - allowed).
            </div>
          </div>

          <!-- Security Status -->
          <div class="mb-3">
            <label for="security-review-status" class="form-label"
              >Security Status <span class="text-danger">*</span></label
            >
            <select
              class="form-select"
              id="security-review-status"
              required
            >
              <option value="" selected disabled>Select a status...</option>
              <option value="Compliant">Compliant</option>
              <option value="Non-Compliant">Non-Compliant</option>
              <option value="Risk Accepted">Risk Accepted</option>
              <option value="Under Review">Under Review</option>
              <option value="Critical Vulnerability">
                Critical Vulnerability
              </option>
              <option value="MUST REVIEW">MUST REVIEW</option>
            </select>
            <div class="invalid-feedback">
              Please select a security status.
            </div>
          </div>

          <!-- Review Notes -->
          <div class="mb-3">
            <label for="security-review-notes" class="form-label"
              >Review Notes (Markdown supported)</label
            >
            <textarea
              class="form-control"
              id="security-review-notes"
              rows="5"
              placeholder="Enter detailed security review notes here..."
              maxlength="5000"
            ></textarea>
            <div class="form-text">
              0/5000 characters. Markdown formatting is supported.
            </div>
            <div class="invalid-feedback">
              Notes cannot exceed 5000 characters.
            </div>
          </div>

          <div class="form-text text-muted mb-3">
            <span class="text-danger">*</span> Required fields
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-secondary"
          data-bs-dismiss="modal"
        >
          Cancel
        </button>
        <button
          type="button"
          class="btn btn-primary"
          id="submit-security-review"
        >
          Submit Review
        </button>
      </div>
    </div>
  </div>
</div>
