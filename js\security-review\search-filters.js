// Security Review Search Filters and Criteria Management
// This file handles search filtering logic and criteria processing

// Helper function to extract endpoint information from complex data structure
function extractEndpointInfo(endpoint) {
  if (!endpoint) {
    return {
      route: "Unknown Route",
      methods: ["Unknown"],
      methodString: "Unknown"
    };
  }
  
  // The endpoint should now be the properly extracted data from getEndpointById
  // So we can access properties directly
  const route = endpoint.route || "Unknown Route";
  const methods = endpoint.httpMethods && Array.isArray(endpoint.httpMethods) 
    ? endpoint.httpMethods 
    : ["Unknown"];
  
  return {
    route: route,
    methods: methods,
    methodString: methods.join(", ")
  };
}

// Get search criteria from the form
function getSearchCriteria() {
  const criteria = {};
  
  // Endpoint search
  const endpointSearch = document.getElementById("search-endpoint");
  if (endpointSearch && endpointSearch.value.trim()) {
    criteria.endpointSearch = endpointSearch.value.trim();
  }
  
  // Reviewer search
  const reviewerSearch = document.getElementById("search-reviewer");
  if (reviewerSearch && reviewerSearch.value.trim()) {
    criteria.reviewerUsername = reviewerSearch.value.trim();
  }
  
  // Notes search
  const notesSearch = document.getElementById("search-notes");
  if (notesSearch && notesSearch.value.trim()) {
    criteria.notesSearch = notesSearch.value.trim();
  }
  
  // Security status filter
  const statusCheckboxes = document.querySelectorAll("#advancedSearchForm input[type='checkbox']:checked");
  const selectedStatuses = Array.from(statusCheckboxes).map(cb => cb.value);
  if (selectedStatuses.length > 0) {
    criteria.securityStatus = selectedStatuses;
  }
  
  // Date range filter
  const dateFromInput = document.getElementById("search-date-from");
  const dateToInput = document.getElementById("search-date-to");
  
  if (dateFromInput && dateFromInput.value) {
    criteria.startDate = dateFromInput.value;
  }
  
  if (dateToInput && dateToInput.value) {
    criteria.endDate = dateToInput.value;
  }
  
  return criteria;
}

// Filter reviews by search criteria
function filterReviewsByCriteria(reviews, criteria) {
  return reviews.filter(review => {
    // Filter by endpoint (route or HTTP method)
    if (criteria.endpointSearch) {
      const endpoint = getEndpointById(review.endpointId);
      const endpointInfo = extractEndpointInfo(endpoint);
      const searchTerm = criteria.endpointSearch.toLowerCase();
      
      const routeMatches = endpointInfo.route.toLowerCase().includes(searchTerm);
      const methodMatches = endpointInfo.methodString.toLowerCase().includes(searchTerm);
      
      if (!routeMatches && !methodMatches) {
        return false;
      }
    }
    
    // Filter by reviewer
    if (criteria.reviewerUsername) {
      const searchTerm = criteria.reviewerUsername.toLowerCase();
      if (!review.reviewerUsername.toLowerCase().includes(searchTerm)) {
        return false;
      }
    }
    
    // Filter by notes content
    if (criteria.notesSearch) {
      const searchTerm = criteria.notesSearch.toLowerCase();
      const notes = review.reviewNotes || "";
      if (!notes.toLowerCase().includes(searchTerm)) {
        return false;
      }
    }
    
    // Filter by security status
    if (criteria.securityStatus && criteria.securityStatus.length > 0) {
      if (!criteria.securityStatus.includes(review.securityStatus)) {
        return false;
      }
    }
    
    // Filter by date range
    if (criteria.startDate) {
      const startDate = new Date(criteria.startDate);
      const reviewDate = new Date(review.reviewDateTime);
      if (reviewDate < startDate) {
        return false;
      }
    }
    
    if (criteria.endDate) {
      const endDate = new Date(criteria.endDate);
      endDate.setHours(23, 59, 59, 999); // End of day
      const reviewDate = new Date(review.reviewDateTime);
      if (reviewDate > endDate) {
        return false;
      }
    }
    
    return true;
  });
}

// Sort search results
function sortSearchResults(sortBy, sortOrder = "asc") {
  console.log(`Sorting search results by ${sortBy} in ${sortOrder} order`);
  
  const tbody = document.getElementById("search-results-tbody");
  if (!tbody) {
    console.warn("Search results table body not found");
    return;
  }
  
  const rows = Array.from(tbody.querySelectorAll("tr"));
  if (rows.length === 0) {
    console.warn("No rows found in search results table");
    return;
  }
  
  // Define sort functions
  const sortFunctions = {
    date: (a, b) => {
      const aValue = parseInt(a.querySelector("td:first-child").getAttribute("data-sort-value") || "0", 10);
      const bValue = parseInt(b.querySelector("td:first-child").getAttribute("data-sort-value") || "0", 10);
      return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
    },
    endpoint: (a, b) => {
      const aValue = a.querySelector("td:nth-child(2)").textContent.toLowerCase();
      const bValue = b.querySelector("td:nth-child(2)").textContent.toLowerCase();
      return sortOrder === "asc" 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    },
    method: (a, b) => {
      const aValue = a.querySelector("td:nth-child(3)").textContent.toLowerCase();
      const bValue = b.querySelector("td:nth-child(3)").textContent.toLowerCase();
      return sortOrder === "asc" 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    },
    reviewer: (a, b) => {
      const aValue = a.querySelector("td:nth-child(4)").textContent.toLowerCase();
      const bValue = b.querySelector("td:nth-child(4)").textContent.toLowerCase();
      return sortOrder === "asc" 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    },
    status: (a, b) => {
      const aValue = a.querySelector("td:nth-child(5)").textContent.toLowerCase();
      const bValue = b.querySelector("td:nth-child(5)").textContent.toLowerCase();
      return sortOrder === "asc" 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    }
  };
  
  // Sort the rows
  rows.sort(sortFunctions[sortBy] || sortFunctions.date);
  
  // Remove all rows from the table
  while (tbody.firstChild) {
    tbody.removeChild(tbody.firstChild);
  }
  
  // Add the sorted rows back to the table
  rows.forEach(row => {
    tbody.appendChild(row);
  });
}

// Update sort button states for results table
function updateSearchResultsSortButtonStates(activeSortBy, activeSortOrder) {
  const sortButtons = document.querySelectorAll(".results-sort-btn");
  
  sortButtons.forEach(button => {
    const sortBy = button.getAttribute("data-sort-by");
    
    // Reset all buttons
    button.classList.remove("active");
    button.textContent = button.textContent.replace(" ↑", "").replace(" ↓", "");
    
    // Update the active button
    if (sortBy === activeSortBy) {
      button.classList.add("active");
      button.setAttribute("data-sort-order", activeSortOrder);
      button.textContent = button.textContent + (activeSortOrder === "asc" ? " ↑" : " ↓");
    }
  });
}

// Generate CSV content from search results
function generateCSVContent(results) {
  const headers = [
    "Review Date",
    "Endpoint Route",
    "HTTP Method", 
    "Reviewer",
    "Security Status",
    "Review Notes"
  ];
  
  // Create CSV rows
  const rows = results.map(review => {
    const endpoint = getEndpointById(review.endpointId);
    const endpointInfo = extractEndpointInfo(endpoint);
    
    return [
      escapeCSVField(formatReviewDate(review.reviewDateTime)),
      escapeCSVField(endpointInfo.route),
      escapeCSVField(endpointInfo.methodString),
      escapeCSVField(review.reviewerUsername),
      escapeCSVField(review.securityStatus),
      escapeCSVField(review.reviewNotes || "")
    ];
  });
  
  // Combine headers and rows
  const allRows = [headers, ...rows];
  
  // Convert to CSV string
  return allRows.map(row => row.join(",")).join("\n");
}

// Escape CSV field (handle commas, quotes, newlines)
function escapeCSVField(field) {
  if (typeof field !== 'string') {
    field = String(field);
  }
  
  // If field contains comma, quote, or newline, wrap in quotes and escape internal quotes
  if (field.includes(',') || field.includes('"') || field.includes('\n')) {
    field = '"' + field.replace(/"/g, '""') + '"';
  }
  
  return field;
}

// Advanced filter operations
function createAdvancedFilter(criteria) {
  return {
    criteria: criteria,
    
    // Check if a review matches all criteria
    matches: function(review) {
      return filterReviewsByCriteria([review], this.criteria).length > 0;
    },
    
    // Apply filter to a list of reviews
    apply: function(reviews) {
      return filterReviewsByCriteria(reviews, this.criteria);
    },
    
    // Get a description of the current filter
    getDescription: function() {
      const parts = [];
      
      if (this.criteria.endpointSearch) {
        parts.push(`Endpoint contains "${this.criteria.endpointSearch}"`);
      }
      
      if (this.criteria.reviewerUsername) {
        parts.push(`Reviewer contains "${this.criteria.reviewerUsername}"`);
      }
      
      if (this.criteria.notesSearch) {
        parts.push(`Notes contain "${this.criteria.notesSearch}"`);
      }
      
      if (this.criteria.securityStatus && this.criteria.securityStatus.length > 0) {
        parts.push(`Status is ${this.criteria.securityStatus.join(" or ")}`);
      }
      
      if (this.criteria.startDate) {
        parts.push(`Date from ${this.criteria.startDate}`);
      }
      
      if (this.criteria.endDate) {
        parts.push(`Date to ${this.criteria.endDate}`);
      }
      
      return parts.length > 0 ? parts.join(" AND ") : "No filters applied";
    },
    
    // Check if filter is empty (no criteria)
    isEmpty: function() {
      return Object.keys(this.criteria).length === 0;
    }
  };
}

// Validate search criteria
function validateSearchCriteria(criteria) {
  const errors = [];
  
  // Validate date range
  if (criteria.startDate && criteria.endDate) {
    const startDate = new Date(criteria.startDate);
    const endDate = new Date(criteria.endDate);
    
    if (startDate > endDate) {
      errors.push("Start date must be before or equal to end date");
    }
  }
  
  // Validate search terms are not too short
  if (criteria.endpointSearch && criteria.endpointSearch.length < 2) {
    errors.push("Endpoint search term must be at least 2 characters");
  }
  
  if (criteria.reviewerUsername && criteria.reviewerUsername.length < 2) {
    errors.push("Reviewer search term must be at least 2 characters");
  }
  
  if (criteria.notesSearch && criteria.notesSearch.length < 3) {
    errors.push("Notes search term must be at least 3 characters");
  }
  
  return errors;
}

// Get search suggestions based on existing data
function getSearchSuggestions() {
  const allReviews = getAllSecurityReviews();
  
  // Get unique reviewers
  const reviewers = [...new Set(allReviews.map(r => r.reviewerUsername))].sort();
  
  // Get unique endpoints
  const endpoints = [];
  allReviews.forEach(review => {
    const endpoint = getEndpointById(review.endpointId);
    if (endpoint) {
      const info = extractEndpointInfo(endpoint);
      endpoints.push({
        route: info.route,
        methods: info.methodString,
        display: `${info.methodString} ${info.route}`
      });
    }
  });
  
  // Remove duplicates
  const uniqueEndpoints = endpoints.filter((endpoint, index, self) => 
    index === self.findIndex(e => e.display === endpoint.display)
  ).sort((a, b) => a.display.localeCompare(b.display));
  
  return {
    reviewers: reviewers,
    endpoints: uniqueEndpoints,
    statuses: getValidSecurityStatuses()
  };
}

// Export for browser environment
if (typeof window !== "undefined") {
  window.extractEndpointInfo = extractEndpointInfo;
  window.getSearchCriteria = getSearchCriteria;
  window.filterReviewsByCriteria = filterReviewsByCriteria;
  window.sortSearchResults = sortSearchResults;
  window.updateSearchResultsSortButtonStates = updateSearchResultsSortButtonStates;
  window.generateCSVContent = generateCSVContent;
  window.escapeCSVField = escapeCSVField;
  window.createAdvancedFilter = createAdvancedFilter;
  window.validateSearchCriteria = validateSearchCriteria;
  window.getSearchSuggestions = getSearchSuggestions;
}

// Export for module environment
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    extractEndpointInfo,
    getSearchCriteria,
    filterReviewsByCriteria,
    sortSearchResults,
    updateSearchResultsSortButtonStates,
    generateCSVContent,
    escapeCSVField,
    createAdvancedFilter,
    validateSearchCriteria,
    getSearchSuggestions
  };
}
