using Microsoft.AspNetCore.Mvc;
using SecurityReviewApi.Models;
using System.Text.Json;

namespace SecurityReviewApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Produces("application/json")] // Informs Swagger that all actions produce JSON
    public class SecurityReviewController : ControllerBase
    {
        const string DataFolderName = "../Data";
        const string DataFileName = "anon-security.json";

        private readonly IWebHostEnvironment _hostingEnvironment;
        private readonly string _filePath;

        public SecurityReviewController(IWebHostEnvironment hostingEnvironment)
        {
            _hostingEnvironment = hostingEnvironment;
            _filePath = Path.Combine(_hostingEnvironment.ContentRootPath, DataFolderName, DataFileName);
        }

        // GET: api/SecurityReview/Load
        [HttpGet("Load")]
        [ProducesResponseType(typeof(SecurityReviewData), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Load()
        {
            try
            {
                if (!System.IO.File.Exists(_filePath))
                {
                    return NotFound("The data file was not found.");
                }

                var json = await System.IO.File.ReadAllTextAsync(_filePath);
                var data = JsonSerializer.Deserialize<SecurityReviewData>(json);

                return Ok(data);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred while reading the file: {ex.Message}");
            }
        }

        // POST: api/SecurityReview/Save
        [HttpPost("Save")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> Save([FromBody] SecurityReviewData data)
        {
            if (data == null)
            {
                return BadRequest("Invalid data provided.");
            }

            try
            {
                // Update metadata before saving
                data.Metadata.LastModified = DateTime.UtcNow;
                data.Metadata.TotalReviews = data.SecurityReviews.Count;

                var options = new JsonSerializerOptions { WriteIndented = true };
                var json = JsonSerializer.Serialize(data, options);

                await System.IO.File.WriteAllTextAsync(_filePath, json);

                return Ok(new { message = "Data saved successfully." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred while saving the file: {ex.Message}");
            }
        }

        // NEW METHOD
        // POST: api/SecurityReview/SaveReview
        [HttpPost("SaveReview")]
        [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> SaveReview([FromBody] SecurityReview newReview)
        {
            if (newReview == null || string.IsNullOrEmpty(newReview.Id))
            {
                return BadRequest("Invalid review data provided. A review ID is required.");
            }

            try
            {
                // Step 1: Read the existing data file
                if (!System.IO.File.Exists(_filePath))
                {
                    return NotFound("The data file was not found.");
                }
                var json = await System.IO.File.ReadAllTextAsync(_filePath);
                var data = JsonSerializer.Deserialize<SecurityReviewData>(json);

                // Step 2: Find if the review already exists
                var existingReview = data.SecurityReviews.FirstOrDefault(r => r.Id == newReview.Id);

                if (existingReview != null)
                {
                    // Update existing review: remove the old one and add the new one
                    // This is a simple way to replace it while maintaining order.
                    var index = data.SecurityReviews.IndexOf(existingReview);
                    data.SecurityReviews[index] = newReview;
                }
                else
                {
                    // Add new review to the top of the list
                    data.SecurityReviews.Insert(0, newReview);
                }

                // Step 3: Update metadata
                data.Metadata.LastModified = DateTime.UtcNow;
                data.Metadata.TotalReviews = data.SecurityReviews.Count;

                // Step 4: Serialize and write the data back to the file
                var options = new JsonSerializerOptions { WriteIndented = true };
                var updatedJson = JsonSerializer.Serialize(data, options);
                await System.IO.File.WriteAllTextAsync(_filePath, updatedJson);

                return Ok(new { message = $"Review '{newReview.Id}' saved successfully.", review = newReview });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"An error occurred while saving the review: {ex.Message}");
            }
        }
    }
}
