<!-- Security Review History Modal -->
<div
  class="modal fade"
  id="securityReviewHistoryModal"
  tabindex="-1"
  aria-labelledby="securityReviewHistoryModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="securityReviewHistoryModalLabel">
          Security Review History
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <!-- Endpoint Information Display -->
        <div class="endpoint-info mb-3 p-2 border rounded bg-light">
          <div class="row">
            <div class="col-md-4">
              <strong>HTTP Method:</strong>
              <span id="history-http-method"></span>
            </div>
            <div class="col-md-8">
              <strong>Route:</strong>
              <span id="history-route"></span>
            </div>
          </div>
        </div>

        <!-- No Reviews Message -->
        <div id="no-reviews-message" class="alert alert-info d-none">
          No security reviews found for this endpoint.
        </div>

        <!-- Review History Table -->
        <div id="review-history-container">
          <div class="mb-3">
            <div class="d-flex justify-content-between align-items-center">
              <h6 class="mb-0">Review History</h6>
              <div
                class="btn-group btn-group-sm"
                role="group"
                aria-label="Sort options"
              >
                <button
                  type="button"
                  class="btn btn-outline-secondary sort-btn"
                  data-sort-by="date"
                  data-sort-order="desc"
                >
                  Date ↓
                </button>
                <button
                  type="button"
                  class="btn btn-outline-secondary sort-btn"
                  data-sort-by="reviewer"
                >
                  Reviewer
                </button>
                <button
                  type="button"
                  class="btn btn-outline-secondary sort-btn"
                  data-sort-by="status"
                >
                  Status
                </button>
              </div>
            </div>
          </div>

          <div class="table-responsive">
            <table
              class="table table-hover table-striped"
              id="review-history-table"
            >
              <thead>
                <tr>
                  <th style="width: 20%">Date/Time</th>
                  <th style="width: 15%">Reviewer</th>
                  <th style="width: 15%">Status</th>
                  <th style="width: 50%">Notes</th>
                </tr>
              </thead>
              <tbody id="review-history-tbody">
                <!-- Review history rows will be populated here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-secondary"
          data-bs-dismiss="modal"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div>
