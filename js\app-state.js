// Application State Management
// Centralized state management for the Endpoint Explorer application

// Global state variables
let endpointData = [];
let filteredData = [];
let currentEndpoint = null;

let currentSort = {
  column: null,
  direction: null,
};

let currentFontSize = 1; // Default font size multiplier
let sidebarCollapsed = false; // Track sidebar state
let lastSidebarWidth = null; // Store last sidebar width for restoration
let detailPinned = false; // Track property sheet pin state
let compactViewEnabled = false; // Track compact view state
let tooltipsEnabled = false; // Default to false (tooltips hidden)

// Column resizing state
let isAnyColumnResizing = false;
let columnResizeStartX;
let columnResizeStartWidth;
let columnResizeTargetHeader;
let columnResizeNextHeader;
let columnResizeNextHeaderStartWidth;
let columnResizeTableTotalWidth;
let MIN_COLUMN_WIDTH = 80; // Default minimum width
let defaultMinColumnWidths = [60, 120, 120, 120, 120, 120, 100, 120]; // CSS-defined minimums per column
const MAX_COLUMN_WIDTH = 1920; // px
let defaultMaxColumnWidths = [100, 180, 1920, 1920, 200, 200, 150, 1920]; // CSS-defined maximums per column
let defaultColumnWidths = []; // To store initial CSS-defined widths
let columnResizerInitialized = false; // Flag to ensure one-time initialization
let lastColumnResizeClick = 0;
let lastColumnResizeTarget = null;

// State accessors and mutators
const AppState = {
  // Data state
  getEndpointData: () => endpointData,
  setEndpointData: (data) => {
    endpointData = data;
    window.endpointData = endpointData;
  },
  
  getFilteredData: () => filteredData,
  setFilteredData: (data) => {
    filteredData = data;
  },
  
  getCurrentEndpoint: () => currentEndpoint,
  setCurrentEndpoint: (endpoint) => {
    currentEndpoint = endpoint;
    window.currentEndpoint = currentEndpoint;
  },
  
  // Sorting state
  getCurrentSort: () => ({ ...currentSort }),
  setCurrentSort: (column, direction) => {
    currentSort.column = column;
    currentSort.direction = direction;
  },
  
  // UI state
  getCurrentFontSize: () => currentFontSize,
  setCurrentFontSize: (size) => {
    currentFontSize = size;
  },
  
  isSidebarCollapsed: () => sidebarCollapsed,
  setSidebarCollapsed: (collapsed) => {
    sidebarCollapsed = collapsed;
  },
  
  getLastSidebarWidth: () => lastSidebarWidth,
  setLastSidebarWidth: (width) => {
    lastSidebarWidth = width;
  },
  
  isDetailPinned: () => detailPinned,
  setDetailPinned: (pinned) => {
    detailPinned = pinned;
  },
  
  isCompactViewEnabled: () => compactViewEnabled,
  setCompactViewEnabled: (enabled) => {
    compactViewEnabled = enabled;
  },
  
  areTooltipsEnabled: () => tooltipsEnabled,
  setTooltipsEnabled: (enabled) => {
    tooltipsEnabled = enabled;
  },
  
  // Column resizing state
  getColumnResizeState: () => ({
    isAnyColumnResizing,
    columnResizeStartX,
    columnResizeStartWidth,
    columnResizeTargetHeader,
    columnResizeNextHeader,
    columnResizeNextHeaderStartWidth,
    columnResizeTableTotalWidth,
    MIN_COLUMN_WIDTH,
    defaultMinColumnWidths: [...defaultMinColumnWidths],
    MAX_COLUMN_WIDTH,
    defaultMaxColumnWidths: [...defaultMaxColumnWidths],
    defaultColumnWidths: [...defaultColumnWidths],
    columnResizerInitialized,
    lastColumnResizeClick,
    lastColumnResizeTarget
  }),
  
  setColumnResizeState: (state) => {
    if (state.hasOwnProperty('isAnyColumnResizing')) isAnyColumnResizing = state.isAnyColumnResizing;
    if (state.hasOwnProperty('columnResizeStartX')) columnResizeStartX = state.columnResizeStartX;
    if (state.hasOwnProperty('columnResizeStartWidth')) columnResizeStartWidth = state.columnResizeStartWidth;
    if (state.hasOwnProperty('columnResizeTargetHeader')) columnResizeTargetHeader = state.columnResizeTargetHeader;
    if (state.hasOwnProperty('columnResizeNextHeader')) columnResizeNextHeader = state.columnResizeNextHeader;
    if (state.hasOwnProperty('columnResizeNextHeaderStartWidth')) columnResizeNextHeaderStartWidth = state.columnResizeNextHeaderStartWidth;
    if (state.hasOwnProperty('columnResizeTableTotalWidth')) columnResizeTableTotalWidth = state.columnResizeTableTotalWidth;
    if (state.hasOwnProperty('MIN_COLUMN_WIDTH')) MIN_COLUMN_WIDTH = state.MIN_COLUMN_WIDTH;
    if (state.hasOwnProperty('defaultMinColumnWidths')) defaultMinColumnWidths = state.defaultMinColumnWidths;
    if (state.hasOwnProperty('defaultMaxColumnWidths')) defaultMaxColumnWidths = state.defaultMaxColumnWidths;
    if (state.hasOwnProperty('defaultColumnWidths')) defaultColumnWidths = state.defaultColumnWidths;
    if (state.hasOwnProperty('columnResizerInitialized')) columnResizerInitialized = state.columnResizerInitialized;
    if (state.hasOwnProperty('lastColumnResizeClick')) lastColumnResizeClick = state.lastColumnResizeClick;
    if (state.hasOwnProperty('lastColumnResizeTarget')) lastColumnResizeTarget = state.lastColumnResizeTarget;
  }
};

// Initialize global window references for backward compatibility
window.endpointData = endpointData;
window.currentEndpoint = currentEndpoint;

// Export state manager
window.AppState = AppState;
