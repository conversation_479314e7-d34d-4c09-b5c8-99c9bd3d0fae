// Security Review History Table Management
// This file handles table rendering and sorting for review history

// Sort the review history table
function sortReviewHistoryTable(sortBy, sortOrder = "asc") {
  console.log(`Sorting review history table by ${sortBy} in ${sortOrder} order`);
  
  const tbody = document.getElementById("review-history-tbody");
  if (!tbody) {
    console.warn("Review history table body not found");
    return;
  }
  
  // Get all rows as an array for sorting
  const rows = Array.from(tbody.querySelectorAll("tr"));
  if (rows.length === 0) {
    console.warn("No rows found in review history table");
    return;
  }
  
  // Define sort functions for different columns
  const sortFunctions = {
    date: (a, b) => {
      const aValue = parseInt(a.querySelector("td:first-child").getAttribute("data-sort-value") || "0", 10);
      const bValue = parseInt(b.querySelector("td:first-child").getAttribute("data-sort-value") || "0", 10);
      return sortOrder === "asc" ? aValue - bValue : bValue - aValue;
    },
    reviewer: (a, b) => {
      const aValue = a.querySelector("td:nth-child(2)").textContent.toLowerCase();
      const bValue = b.querySelector("td:nth-child(2)").textContent.toLowerCase();
      return sortOrder === "asc" 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    },
    status: (a, b) => {
      const aValue = a.querySelector("td:nth-child(3)").textContent.toLowerCase();
      const bValue = b.querySelector("td:nth-child(3)").textContent.toLowerCase();
      return sortOrder === "asc" 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue);
    }
  };
  
  // Sort the rows
  rows.sort(sortFunctions[sortBy] || sortFunctions.date);
  
  // Remove all rows from the table
  while (tbody.firstChild) {
    tbody.removeChild(tbody.firstChild);
  }
  
  // Add the sorted rows back to the table
  rows.forEach(row => {
    tbody.appendChild(row);
  });
}

// Create a table structure for review history
function createReviewHistoryTable() {
  const tableContainer = document.createElement("div");
  tableContainer.className = "table-responsive";
  tableContainer.id = "review-history-container";
  
  const table = document.createElement("table");
  table.className = "table table-striped table-hover";
  table.id = "review-history-table";
  
  // Create colgroup for column sizing
  const colgroup = document.createElement("colgroup");
  const cols = [
    { width: "20%" }, // Date
    { width: "15%" }, // Reviewer
    { width: "15%" }, // Status
    { width: "50%" }  // Notes
  ];
  
  cols.forEach(col => {
    const colElement = document.createElement("col");
    if (col.width) colElement.style.width = col.width;
    colgroup.appendChild(colElement);
  });
  
  table.appendChild(colgroup);
  
  // Create table header
  const thead = document.createElement("thead");
  thead.className = "table-dark";
  
  const headerRow = document.createElement("tr");
  const headers = [
    { text: "Review Date", sortBy: "date" },
    { text: "Reviewer", sortBy: "reviewer" },
    { text: "Status", sortBy: "status" },
    { text: "Notes", sortBy: null }
  ];
  
  headers.forEach(header => {
    const th = document.createElement("th");
    th.textContent = header.text;
    
    if (header.sortBy) {
      th.style.cursor = "pointer";
      th.setAttribute("data-sort-by", header.sortBy);
      
      // Add column resizer
      const resizer = document.createElement("div");
      resizer.className = "column-resizer";
      th.appendChild(resizer);
    }
    
    headerRow.appendChild(th);
  });
  
  thead.appendChild(headerRow);
  table.appendChild(thead);
  
  // Create table body
  const tbody = document.createElement("tbody");
  tbody.id = "review-history-tbody";
  table.appendChild(tbody);
  
  tableContainer.appendChild(table);
  
  return tableContainer;
}

// Create sort buttons for the history table
function createHistorySortButtons() {
  const sortContainer = document.createElement("div");
  sortContainer.className = "d-flex justify-content-between align-items-center mb-3";
  
  const title = document.createElement("h6");
  title.className = "mb-0";
  title.textContent = "Review History";
  
  const buttonGroup = document.createElement("div");
  buttonGroup.className = "btn-group btn-group-sm";
  buttonGroup.setAttribute("role", "group");
  buttonGroup.setAttribute("aria-label", "Sort options");
  
  const sortButtons = [
    { text: "Date", sortBy: "date", order: "desc" },
    { text: "Reviewer", sortBy: "reviewer", order: "asc" },
    { text: "Status", sortBy: "status", order: "asc" }
  ];
  
  sortButtons.forEach((btn, index) => {
    const button = document.createElement("button");
    button.type = "button";
    button.className = `btn btn-outline-secondary sort-btn ${index === 0 ? 'active' : ''}`;
    button.setAttribute("data-sort-by", btn.sortBy);
    button.setAttribute("data-sort-order", btn.order);
    button.textContent = btn.text + (index === 0 ? " ↓" : "");
    
    buttonGroup.appendChild(button);
  });
  
  sortContainer.appendChild(title);
  sortContainer.appendChild(buttonGroup);
  
  return sortContainer;
}

// Initialize column resizing for history table
function initHistoryTableColumnResizing() {
  const table = document.getElementById("review-history-table");
  if (!table) return;
  
  const resizers = table.querySelectorAll("th .column-resizer");
  
  resizers.forEach((resizer, index) => {
    const th = resizer.closest("th");
    if (!th) return;
    
    let startX, startWidth;
    
    resizer.addEventListener("mousedown", function(e) {
      e.preventDefault();
      startX = e.clientX;
      startWidth = parseInt(window.getComputedStyle(th).width, 10);
      
      document.addEventListener("mousemove", resizeColumn);
      document.addEventListener("mouseup", stopResize);
      
      // Add visual feedback
      resizer.classList.add("active");
      document.body.classList.add("resize-active");
    });
    
    function resizeColumn(e) {
      const diff = e.clientX - startX;
      const newWidth = Math.max(80, startWidth + diff); // Minimum width of 80px
      th.style.width = newWidth + "px";
    }
    
    function stopResize() {
      document.removeEventListener("mousemove", resizeColumn);
      document.removeEventListener("mouseup", stopResize);
      
      // Remove visual feedback
      resizer.classList.remove("active");
      document.body.classList.remove("resize-active");
    }
  });
}

// Populate the review history table with review data
function populateReviewHistoryTable(reviews) {
  const tbody = document.getElementById("review-history-tbody");
  if (!tbody) {
    console.error("Review history table body not found");
    return;
  }
  
  // Clear existing content
  tbody.innerHTML = "";
  
  if (!reviews || reviews.length === 0) {
    const emptyRow = document.createElement("tr");
    const emptyCell = document.createElement("td");
    emptyCell.colSpan = 4;
    emptyCell.className = "text-center text-muted p-4";
    emptyCell.innerHTML = '<em>No reviews found for this endpoint</em>';
    emptyRow.appendChild(emptyCell);
    tbody.appendChild(emptyRow);
    return;
  }
  
  // Create rows for each review
  reviews.forEach(review => {
    try {
      const row = createReviewTableRow(review, { includeEndpoint: false });
      tbody.appendChild(row);
    } catch (error) {
      console.error("Error creating review table row:", error);
    }
  });
  
  // Initialize column resizing after populating
  setTimeout(() => {
    initHistoryTableColumnResizing();
  }, 100);
}

// Clear the review history table
function clearReviewHistoryTable() {
  const tbody = document.getElementById("review-history-tbody");
  if (tbody) {
    tbody.innerHTML = "";
  }
}

// Show loading state in the history table
function showHistoryTableLoading() {
  const tbody = document.getElementById("review-history-tbody");
  if (tbody) {
    tbody.innerHTML = `
      <tr>
        <td colspan="4" class="text-center p-4">
          <div class="d-flex align-items-center justify-content-center">
            <div class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></div>
            <span>Loading review history...</span>
          </div>
        </td>
      </tr>`;
  }
}

// Show error state in the history table
function showHistoryTableError(message, retryCallback) {
  const tbody = document.getElementById("review-history-tbody");
  if (tbody) {
    tbody.innerHTML = `
      <tr>
        <td colspan="4" class="text-center p-4 text-danger">
          <div class="d-flex align-items-center justify-content-center">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <span>Error: ${message}</span>
          </div>
          ${retryCallback ? `
            <div class="mt-2">
              <button class="btn btn-sm btn-outline-primary" onclick="${retryCallback}">
                <i class="bi bi-arrow-clockwise"></i> Retry
              </button>
            </div>
          ` : ''}
        </td>
      </tr>`;
  }
}

// Get current sort state of the history table
function getHistoryTableSortState() {
  const activeSort = document.querySelector(".sort-btn.active");
  if (activeSort) {
    return {
      sortBy: activeSort.getAttribute("data-sort-by"),
      sortOrder: activeSort.getAttribute("data-sort-order")
    };
  }
  return { sortBy: "date", sortOrder: "desc" };
}

// Apply sort state to the history table
function applyHistoryTableSort(sortBy, sortOrder) {
  sortReviewHistoryTable(sortBy, sortOrder);
  updateHistorySortButtonStates(sortBy, sortOrder);
}

// Export for browser environment
if (typeof window !== "undefined") {
  window.sortReviewHistoryTable = sortReviewHistoryTable;
  window.createReviewHistoryTable = createReviewHistoryTable;
  window.createHistorySortButtons = createHistorySortButtons;
  window.initHistoryTableColumnResizing = initHistoryTableColumnResizing;
  window.populateReviewHistoryTable = populateReviewHistoryTable;
  window.clearReviewHistoryTable = clearReviewHistoryTable;
  window.showHistoryTableLoading = showHistoryTableLoading;
  window.showHistoryTableError = showHistoryTableError;
  window.getHistoryTableSortState = getHistoryTableSortState;
  window.applyHistoryTableSort = applyHistoryTableSort;
}

// Export for module environment
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    sortReviewHistoryTable,
    createReviewHistoryTable,
    createHistorySortButtons,
    initHistoryTableColumnResizing,
    populateReviewHistoryTable,
    clearReviewHistoryTable,
    showHistoryTableLoading,
    showHistoryTableError,
    getHistoryTableSortState,
    applyHistoryTableSort
  };
}
