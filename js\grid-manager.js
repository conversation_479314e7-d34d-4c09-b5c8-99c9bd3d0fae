// Grid Rendering and Management Module
// Handles the rendering of the main data grid and tooltips

// Main grid rendering function
function renderGrid() {
  const gridBody = document.getElementById("grid-body");
  const tooltip = document.getElementById("endpoint-tooltip");
  const rowCountSpan = document.getElementById("row-count");

  if (!gridBody || !tooltip) return;

  gridBody.innerHTML = ""; // Clear existing rows

  const filteredData = AppState.getFilteredData();
  const endpointData = AppState.getEndpointData();

  if (!filteredData || filteredData.length === 0) {
    console.log("No filtered data to display");
    const emptyRow = document.createElement("tr");
    emptyRow.innerHTML = `<td colspan="8" style="text-align: center; padding: 20px;">
            ${
              endpointData.length === 0
                ? "No data loaded. Please load a JSON file."
                : "No endpoints found matching the current filters."
            }
        </td>`;
    gridBody.appendChild(emptyRow);

    // Update row count display to show 0 rows
    if (rowCountSpan) {
      rowCountSpan.textContent = "[0 rows]";
    }

    return;
  }

  console.log(`Rendering ${filteredData.length} rows`);

  // Update row count display
  if (rowCountSpan) {
    rowCountSpan.textContent = `[${filteredData.length} rows]`;
  }

  // Update cell widths once before rendering rows, ensuring headers are set.
  if (window.ColumnResizer) {
    window.ColumnResizer.updateAllCellWidths();
  }

  filteredData.forEach((item, index) => {
    const row = document.createElement("tr");

    // Set row class based on diffType
    row.classList.add(`row-${item.diffType.toLowerCase()}`);

    // Get the endpoint object based on diffType
    const endpoint =
      item.diffType === "Removed"
        ? item.oldValue
        : item.newValue || item.oldValue;

    if (!endpoint) {
      console.warn(`No endpoint data for item at index ${index}:`, item);
      return; // Skip this row if endpoint data is missing
    }

    // Add the endpoint ID as a data attribute for event handlers
    if (endpoint.id) {
      row.setAttribute('data-endpoint-id', endpoint.id);
    }

    const httpMethodsDisplay = endpoint.httpMethods
      ? endpoint.httpMethods.join(", ")
      : "";

    // Create cells individually to avoid setting title attribute via innerHTML
    const diffTypeCell = document.createElement("td");
    diffTypeCell.textContent = getDiffTypeAbbreviation(item.diffType);
    diffTypeCell.title = item.diffType; // Show full text on hover
    row.appendChild(diffTypeCell);

    const httpMethodsCell = document.createElement("td");
    httpMethodsCell.textContent = httpMethodsDisplay;
    row.appendChild(httpMethodsCell);

    const routeCell = document.createElement("td");

    // Format route with the first part between two slashes in bold
    const route = endpoint.route || "";
    if (route) {
      // Find the first part between two slashes
      const matches = route.match(/^\/([^\/]+)(\/.*)?$/);
      if (matches && matches[1]) {
        // There's a part between two slashes, make it bold
        const firstPart = matches[1];
        const remainingPart = matches[2] || "";
        routeCell.innerHTML = `/<strong>${firstPart}</strong>${remainingPart}`;
      } else {
        // No matching pattern, display as plain text
        routeCell.textContent = route;
      }
    } else {
      routeCell.textContent = "";
    }

    row.appendChild(routeCell);

    const policyCell = document.createElement("td");
    policyCell.textContent = endpoint.policy || "";
    row.appendChild(policyCell);

    // Security Status cell
    const securityStatusCell = document.createElement("td");
    if (window.createSecurityStatusCell) {
      securityStatusCell.innerHTML = window.createSecurityStatusCell(endpoint);
    }
    row.appendChild(securityStatusCell);

    // Review Date cell
    const reviewDateCell = document.createElement("td");
    if (window.createReviewDateCell) {
      reviewDateCell.innerHTML = window.createReviewDateCell(endpoint);
    }
    row.appendChild(reviewDateCell);

    // Security Actions cell
    const securityActionsCell = document.createElement("td");
    if (window.createSecurityActionsCell) {
      securityActionsCell.innerHTML = window.createSecurityActionsCell(endpoint);
    }
    row.appendChild(securityActionsCell);

    // Request Parameters cell moved to the end as the rightmost column
    const requestParamsCell = document.createElement("td");
    if (endpoint.requestParameters && endpoint.requestParameters.length > 0) {
      // Check if text wrap is enabled for the grid
      const grid = document.getElementById("endpoints-grid");
      const isTextWrapEnabled = grid && grid.classList.contains("text-wrap");

      // Use line breaks for multiline display only when text wrap is enabled
      if (isTextWrapEnabled) {
        requestParamsCell.innerHTML = endpoint.requestParameters.join("<br>");
      } else {
        requestParamsCell.textContent = endpoint.requestParameters.join(", ");
      }
    } else {
      requestParamsCell.textContent = "";
    }
    row.appendChild(requestParamsCell);

    // Add event listeners
    row.addEventListener("click", () => {
      if (window.DetailView) {
        window.DetailView.showDetailView(item);
      }
    });

    // Add mouseover and mouseout event listeners for tooltips
    row.addEventListener("mouseover", (event) => {
      if (AppState.areTooltipsEnabled()) {
        showTooltip(event, endpoint);
      }
    });

    row.addEventListener("mouseout", () => {
      if (AppState.areTooltipsEnabled()) {
        hideTooltip();
      }
    });

    // Right-click to show tooltip temporarily
    row.addEventListener("mousedown", (event) => {
      if (!AppState.areTooltipsEnabled() && event.button === 2) {
        // Right mouse button
        showTooltip(event, endpoint);
        event.preventDefault(); // Prevent context menu
      }
    });

    row.addEventListener("mouseup", (event) => {
      if (!AppState.areTooltipsEnabled() && event.button === 2) {
        // Right mouse button
        hideTooltip();
      }
    });

    // Prevent context menu when right-click is used for tooltip
    row.addEventListener("contextmenu", (event) => {
      if (!AppState.areTooltipsEnabled()) {
        event.preventDefault();
      }
    });

    gridBody.appendChild(row);
  });

  console.log(`Grid rendered with ${gridBody.children.length} rows`);

  // Update sort headers
  if (window.FiltersSortManager) {
    window.FiltersSortManager.updateSortHeaders();
  }

  // Apply compact view if enabled
  const grid = document.getElementById("endpoints-grid");
  if (grid) {
    if (AppState.isCompactViewEnabled()) {
      grid.classList.add("compact");
    } else {
      grid.classList.remove("compact");
    }

    // Ensure request parameters display correctly based on current text wrap setting
    const isTextWrapEnabled = grid.classList.contains("text-wrap");
    if (isTextWrapEnabled && window.ColumnResizer) {
      window.ColumnResizer.updateRequestParametersDisplay(true);
    }
  }

  // Re-initialize security action buttons after grid rendering
  setTimeout(function() {
    if (typeof window.reinitializeSecurityButtons === 'function') {
      window.reinitializeSecurityButtons();
    } else if (typeof window.initSecurityActionButtons === 'function') {
      window.initSecurityActionButtons();
      console.log("🔄 Security action buttons re-initialized after grid render");
    }
  }, 10);
}

// Show tooltip with endpoint data
function showTooltip(event, endpoint) {
  const tooltip = document.getElementById("endpoint-tooltip");
  if (!tooltip) {
    console.error("Tooltip element not found!");
    return;
  }

  if (!endpoint || typeof endpoint !== "object") {
    console.warn("Invalid endpoint data for tooltip:", endpoint);
    hideTooltip(); // Hide if data is invalid
    return;
  }

  // Construct tooltip content
  let tooltipContent = '<ul class="list-unstyled mb-0">';
  let hasContent = false;
  const excludedKeys = [
    "fileName",
    "lineNumber",
    "crC64",
    "excludeFromApiClient",
  ]; // Temporarily exclude excludeFromApiClient to control its position

  // Process properties in a specific order to maintain consistent display
  const orderedKeys = [
    "id",
    "projectName",
    "namespace",
    "controller",
    "actionName",
    "route",
    "httpMethods",
    "policy",
    "description",
    "response",
    "requestParameters",
    "otherAttributes",
    "excludeFromApiClient", // Place excludeFromApiClient after otherAttributes
  ];

  // First, add properties in the ordered list if they exist
  orderedKeys.forEach((key) => {
    if (key === "excludeFromApiClient") {
      // Special handling for excludeFromApiClient to always show as true/false
      const boolValue =
        endpoint.excludeFromApiClient === true ? "true" : "false";
      tooltipContent += `<li><strong>excludeFromApiClient:</strong> ${boolValue}</li>`;
      hasContent = true;
    } else if (key in endpoint && !excludedKeys.includes(key)) {
      const displayValue = formatTooltipValue(endpoint[key]);

      if (
        displayValue &&
        displayValue !== "<em>null/undefined</em>" &&
        displayValue !== "<em>empty array</em>" &&
        displayValue.trim() !== ""
      ) {
        hasContent = true;
        const displayKey = String(key).replace(
          /[&<>\\\\\\"\\'\\`]/g,
          (s) =>
            ({
              "&": "&amp;",
              "<": "&lt;",
              ">": "&gt;",
              '"': "&quot;",
              "'": "&#39;",
              "`": "&#x60;",
            }[s])
        );

        tooltipContent += `<li><strong>${displayKey}:</strong> ${displayValue}</li>`;
      }
    }
  });

  // Then process any remaining properties not in the ordered list
  for (const key in endpoint) {
    if (
      endpoint.hasOwnProperty(key) &&
      !excludedKeys.includes(key) &&
      !orderedKeys.includes(key)
    ) {
      const displayValue = formatTooltipValue(endpoint[key]);

      // Only add to tooltip if displayValue is not empty
      if (
        displayValue &&
        displayValue !== "<em>null/undefined</em>" &&
        displayValue !== "<em>empty array</em>" &&
        displayValue.trim() !== ""
      ) {
        hasContent = true;
        const displayKey = String(key).replace(
          /[&<>\\\\\\"\\'\\`]/g,
          (s) =>
            ({
              "&": "&amp;",
              "<": "&lt;",
              ">": "&gt;",
              '"': "&quot;",
              "'": "&#39;",
              "`": "&#x60;",
            }[s])
        );

        tooltipContent += `<li><strong>${displayKey}:</strong> ${displayValue}</li>`;
      }
    }
  }

  // Add security review information
  const latestReview = window.getLatestSecurityReview ? window.getLatestSecurityReview(endpoint.id) : null;

  if (latestReview && latestReview.securityStatus) {
    // Add security status with appropriate styling class
    const securityStatusClass = window.getSecurityStatusClass ? window.getSecurityStatusClass(latestReview.securityStatus) : "";
    tooltipContent += `<li><strong>Security Status:</strong> <span class="security-status ${securityStatusClass}">${latestReview.securityStatus}</span></li>`;
    hasContent = true;

    // Add review date if available
    if (latestReview.reviewDateTime) {
      const reviewDate = window.formatReviewDate ? window.formatReviewDate(latestReview.reviewDateTime) : latestReview.reviewDateTime;
      tooltipContent += `<li><strong>Last Review:</strong> ${reviewDate}</li>`;
    }
  } else {
    // Show "No Review" status
    tooltipContent += `<li><strong>Security Status:</strong> <span class="security-status security-status-none">No Review</span></li>`;
    hasContent = true;
  }

  tooltipContent += "</ul>";

  if (!hasContent) {
    console.warn("No content to display in tooltip for endpoint:", endpoint);
    hideTooltip();
    return;
  }

  tooltip.innerHTML = tooltipContent;
  console.log("Tooltip innerHTML:", tooltip.innerHTML); // Log the content

  tooltip.style.display = "block";
  tooltip.style.visibility = "visible"; // Explicitly set visibility
  tooltip.style.opacity = "1"; // Explicitly set opacity
  console.log(
    "Tooltip display set to block, visibility to visible, opacity to 1"
  );

  // Position the tooltip near the mouse cursor
  const offsetX = 15;
  const offsetY = 15;

  let x = event.clientX + offsetX;
  let y = event.clientY + offsetY;

  // Ensure tooltip stays within viewport boundaries
  const tooltipRect = tooltip.getBoundingClientRect(); // Get dimensions *after* content is set AND display is block
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  // Adjust if tooltip goes off the right edge
  if (x + tooltipRect.width > viewportWidth) {
    x = event.clientX - tooltipRect.width - offsetX;
  }
  // Adjust if tooltip goes off the bottom edge
  if (y + tooltipRect.height > viewportHeight) {
    y = event.clientY - tooltipRect.height - offsetY;
  }

  // Ensure tooltip is not positioned off-screen (e.g., negative coordinates)
  if (x < 0) x = offsetX;
  if (y < 0) y = offsetY;

  tooltip.style.left = `${x}px`;
  tooltip.style.top = `${y}px`;
  console.log(
    `Tooltip positioned at: x=${x}, y=${y}, width=${tooltipRect.width}, height=${tooltipRect.height}`
  );
}

// Hide tooltip
function hideTooltip() {
  const tooltip = document.getElementById("endpoint-tooltip");
  if (tooltip) {
    tooltip.style.display = "none";
    tooltip.style.visibility = "hidden"; // Also hide visibility
    tooltip.style.opacity = "0"; // Reset opacity
    console.log(
      "Tooltip display set to none, visibility to hidden, opacity to 0"
    );
  }
}

// Format tooltip value (e.g., handle arrays or objects)
function formatTooltipValue(value) {
  if (
    value === null ||
    typeof value === "undefined" ||
    String(value).trim() === ""
  ) {
    return ""; // Return empty string for null, undefined, or empty string values
  }
  if (Array.isArray(value)) {
    if (value.length === 0) return ""; // Return empty string for empty arrays
    const formattedArray = value
      .map(formatTooltipValue)
      .filter((v) => v !== ""); // Filter out empty strings
    return formattedArray.length > 0 ? formattedArray.join("<br>") : ""; // Join with line breaks instead of commas
  }
  // Special handling for boolean values to ensure they are always displayed
  if (typeof value === "boolean") {
    return value.toString(); // Convert to "true" or "false" string
  }
  if (typeof value === "object") {
    // Simple object to string, could be more sophisticated for nested objects
    try {
      const jsonString = JSON.stringify(value);
      // Prevent overly long strings in tooltips for large objects
      if (jsonString === "{}" || jsonString === "[]") return ""; // Return empty for empty objects/arrays
      return jsonString.length > 100
        ? jsonString.substring(0, 97) + "..."
        : jsonString;
    } catch (e) {
      return "<em>[object]</em>"; // Fallback for circular or problematic objects
    }
  }
  // Sanitize string values before displaying
  return String(value).replace(
    /[&<>"'`]/g,
    (s) =>
      ({
        "&": "&amp;",
        "<": "&lt;",
        ">": "&gt;",
        '"': "&quot;",
        "'": "&#39;",
        "`": "&#x60;",
      }[s])
  );
}

// Helper function to get diff type abbreviation
function getDiffTypeAbbreviation(diffType) {
  if (!diffType) return "";

  // Extract first letter of each capital letter word
  const matches = diffType.match(/[A-Z]/g);
  return matches ? matches[0] : diffType.charAt(0).toUpperCase();
}

// Helper function to format values for display
function formatValueForDisplay(value, fieldName = null) {
  // Special handling for boolean fields when value is undefined/null
  if (
    (value === undefined || value === null) &&
    fieldName === "excludeFromApiClient"
  ) {
    return "false";
  }

  if (value === undefined || value === null) {
    return "";
  } else if (typeof value === "boolean") {
    // Explicitly handle boolean values
    return String(value);
  } else if (Array.isArray(value)) {
    // Use the same sanitization for array items
    return value
      .map((item) => {
        if (typeof item === "string") {
          return String(item).replace(
            /[&<>"'`]/g,
            (s) =>
              ({
                "&": "&amp;",
                "<": "&lt;",
                ">": "&gt;",
                '"': "&quot;",
                "'": "&#39;",
                "`": "&#x60;",
              }[s])
          );
        }
        return String(item);
      })
      .join("<br>");
  } else if (typeof value === "object") {
    return JSON.stringify(value);
  } else if (typeof value === "string" && value.match(/^\d{4}-\d{2}-\d{2}T/)) {
    // Format dates
    return new Date(value).toLocaleString();
  } else {
    // Sanitize string values before displaying
    return String(value).replace(
      /[&<>"'`]/g,
      (s) =>
        ({
          "&": "&amp;",
          "<": "&lt;",
          ">": "&gt;",
          '"': "&quot;",
          "'": "&#39;",
          "`": "&#x60;",
        }[s])
    );
  }
}

// Export functions for use by other modules
window.GridManager = {
  renderGrid,
  showTooltip,
  hideTooltip,
  formatTooltipValue,
  getDiffTypeAbbreviation,
  formatValueForDisplay
};

// Make renderGrid available in the global scope for backward compatibility
window.renderGrid = renderGrid;
