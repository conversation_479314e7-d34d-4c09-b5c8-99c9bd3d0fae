// Security Review Fixes and Overrides
// This file contains fixes for security review functionality to ensure proper integration

// Flag to track if we've already modified the createSecurityReview function
let createSecurityReviewModified = false;

// Override the createSecurityReview function to prevent duplicate entries
document.addEventListener("DOMContentLoaded", function() {
  console.log("Security review fix script loaded");
  
  // Only modify the function if it hasn't been modified yet
  if (!createSecurityReviewModified && typeof createSecurityReview === 'function') {
    // Store the original function
    const originalCreateSecurityReview = createSecurityReview;
    
    // Override the function to add deduplication
    window.createSecurityReview = function(reviewData) {
      console.log("🔄 createSecurityReview override called with:", reviewData);
      
      // Check for existing reviews for this endpoint within the last minute
      const existingReviews = getSecurityReviewsForEndpoint(reviewData.endpointId);
      console.log(`📊 Found ${existingReviews.length} existing reviews for endpoint ${reviewData.endpointId}`);
      
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60000);
      
      const recentDuplicate = existingReviews.find(review => {
        const reviewTime = new Date(review.reviewDateTime);
        const isRecent = reviewTime >= oneMinuteAgo;
        const sameUser = review.reviewerUsername === reviewData.reviewerUsername;
        const sameStatus = review.securityStatus === reviewData.securityStatus;
        
        console.log(`🔍 Checking review ${review.id}:`);
        console.log(`  - Is recent (within 1 min): ${isRecent}`);
        console.log(`  - Same user: ${sameUser} (${review.reviewerUsername} vs ${reviewData.reviewerUsername})`);
        console.log(`  - Same status: ${sameStatus} (${review.securityStatus} vs ${reviewData.securityStatus})`);
        
        return isRecent && sameUser && sameStatus;
      });
      
      if (recentDuplicate) {
        console.warn("⚠️ Duplicate review detected, skipping creation");
        console.warn("Duplicate review:", recentDuplicate);
        return recentDuplicate;
      }
      
      console.log("✅ No duplicates found, proceeding with original creation");
      
      // Call the original function
      const result = originalCreateSecurityReview(reviewData);
      console.log("✅ Original createSecurityReview returned:", result);
      return result;
    };
    
    createSecurityReviewModified = true;
    console.log("Successfully modified createSecurityReview to prevent duplicates");
  }
  
  // Override showSuccessMessage to use a toast notification instead of an alert
  window.showSuccessMessage = function(message) {
    console.log("Success message:", message);
    
    // Create a toast notification element
    const toastContainer = document.createElement("div");
    toastContainer.style.position = "fixed";
    toastContainer.style.bottom = "20px";
    toastContainer.style.right = "20px";
    toastContainer.style.zIndex = "9999";
    
    const toast = document.createElement("div");
    toast.className = "toast show";
    toast.setAttribute("role", "alert");
    toast.setAttribute("aria-live", "assertive");
    toast.setAttribute("aria-atomic", "true");
    
    toast.innerHTML = `
      <div class="toast-header bg-success text-white">
        <strong class="me-auto">Success</strong>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
      </div>
      <div class="toast-body">
        ${message}
      </div>
    `;
    
    toastContainer.appendChild(toast);
    document.body.appendChild(toastContainer);
    
    // Add close button functionality
    const closeButton = toast.querySelector(".btn-close");
    if (closeButton) {
      closeButton.addEventListener("click", function() {
        toastContainer.remove();
      });
    }
    
    // Auto-remove after 3 seconds
    setTimeout(function() {
      if (toastContainer.parentNode) {
        toastContainer.remove();
      }
    }, 3000);
  };
  
  // Add click handler for security status cells
  document.addEventListener("click", function(e) {
    // Check if the clicked element is a security status cell or its child
    const securityStatusElement = e.target.closest(".security-status");
    if (securityStatusElement) {
      e.stopPropagation();
      console.log("Security status cell clicked - opening Add Review dialog");
      
      // Find the closest row and get the endpoint data
      const row = securityStatusElement.closest("tr");
      if (row) {
        const endpointId = row.getAttribute("data-endpoint-id");
        if (endpointId) {
          const endpoint = getEndpointById(endpointId);
          if (endpoint && typeof showSecurityReviewModal === 'function') {
            // Open Add Review modal instead of history modal
            showSecurityReviewModal(endpoint);
          } else {
            console.warn("Could not find endpoint or showSecurityReviewModal function");
          }
        } else {
          console.warn("Could not find endpoint ID for security status click");
        }
      }
    }
  }, true); // Use capture phase to ensure this handler runs before others
  
  // Fix the issue with the detail view opening when clicking security action buttons
  // Add a global click handler to prevent row click events when clicking on security action buttons
  document.addEventListener("click", function(e) {
    const securityActionBtn = e.target.closest(".security-action-btn");
    if (securityActionBtn) {
      e.stopPropagation();
      console.log("Security action button clicked, preventing row click");
      
      // Handle the button click functionality here since we're stopping propagation
      const endpointId = securityActionBtn.getAttribute("data-endpoint-id");
      const httpMethod = securityActionBtn.getAttribute("data-http-method");
      const route = securityActionBtn.getAttribute("data-route");
      
      console.log(`Handling security button click: ${endpointId}, ${httpMethod}, ${route}`);
      
      // Create endpoint object
      const endpoint = {
        id: endpointId,
        httpMethods: httpMethod ? httpMethod.split(", ") : ["Unknown"],
        route: route || "Unknown Route"
      };
      
      if (securityActionBtn.classList.contains("add-review-btn")) {
        console.log("Add review button clicked");
        if (typeof showSecurityReviewModal === 'function') {
          showSecurityReviewModal(endpoint);
        } else {
          console.error("showSecurityReviewModal function not available");
        }
      } else if (securityActionBtn.classList.contains("view-history-btn")) {
        console.log("View history button clicked");
        if (typeof showSecurityReviewHistoryModal === 'function') {
          showSecurityReviewHistoryModal(endpoint);
        } else {
          console.error("showSecurityReviewHistoryModal function not available");
        }
      }
    }
  }, true); // Use capture phase to ensure this handler runs before others
  
  // Fix the sort buttons in the security review history modal
  document.addEventListener("click", function(e) {
    const sortBtn = e.target.closest(".sort-btn");
    if (sortBtn) {
      e.stopPropagation();
      console.log("Sort button clicked");
    }
  });
  
  // Add click handler for review history table headers
  document.addEventListener("click", function(e) {
    // Check if the clicked element is a table header in the review history table
    const headerCell = e.target.closest("#review-history-table th");
    if (headerCell) {
      e.stopPropagation();
      console.log("Review history table header clicked");
    }
  });
  
  // Initialize security review history functionality
  if (typeof initSecurityReviewHistory === 'function') {
    initSecurityReviewHistory();
  } else {
    console.warn("initSecurityReviewHistory function not found");
  }
  
  // Note: Column resizing is handled by the main column-resizer.js module
  
  // Function to make table columns resizable
  function makeColumnsResizable(table) {
    const cols = table.querySelectorAll('colgroup col');
    const headers = table.querySelectorAll('th');
    
    headers.forEach((header, index) => {
      if (index < headers.length - 1) { // Don't add resizer to last column
        const resizer = document.createElement('div');
        resizer.className = 'column-resizer';
        header.appendChild(resizer);
        
        let startX, startWidth;
        
        resizer.addEventListener('mousedown', function(e) {
          e.preventDefault();
          startX = e.clientX;
          startWidth = parseInt(window.getComputedStyle(header).width, 10);
          
          document.addEventListener('mousemove', resizeColumn);
          document.addEventListener('mouseup', stopResize);
          
          resizer.classList.add('active');
          document.body.classList.add('resize-active');
        });
        
        function resizeColumn(e) {
          const diff = e.clientX - startX;
          const newWidth = Math.max(80, startWidth + diff);
          header.style.width = newWidth + 'px';
          if (cols[index]) {
            cols[index].style.width = newWidth + 'px';
          }
        }
        
        function stopResize() {
          document.removeEventListener('mousemove', resizeColumn);
          document.removeEventListener('mouseup', stopResize);
          
          resizer.classList.remove('active');
          document.body.classList.remove('resize-active');
        }
      }
    });
  }
  
  // Add direct column resizing to any existing tables
  const existingTables = document.querySelectorAll('#review-history-table');
  existingTables.forEach(table => {
    if (!table.querySelector('colgroup')) {
      const colgroup = document.createElement('colgroup');
      const colWidths = ['20%', '15%', '15%', '50%'];
      colWidths.forEach(width => {
        const col = document.createElement('col');
        col.style.width = width;
        colgroup.appendChild(col);
      });
      table.insertBefore(colgroup, table.firstChild);
    }
    makeColumnsResizable(table);
  });
  
  // Apply the modal width change to any existing modals
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      mutation.addedNodes.forEach(function(node) {
        if (node.nodeType === Node.ELEMENT_NODE) {
          // Check for security review history modal
          const historyModal = node.querySelector ? node.querySelector('#securityReviewHistoryModal') : null;
          if (historyModal || (node.id === 'securityReviewHistoryModal')) {
            const modalDialog = (historyModal || node).querySelector('.modal-dialog');
            if (modalDialog) {
              modalDialog.style.maxWidth = '60%';
              modalDialog.style.width = '60%';
            }
          }
          
          // Check for review history table
          const reviewTable = node.querySelector ? node.querySelector('#review-history-table') : null;
          if (reviewTable || (node.id === 'review-history-table')) {
            makeColumnsResizable(reviewTable || node);
          }
        }
      });
    });
  });
  
  observer.observe(document.body, { childList: true, subtree: true });
});

// Override the refreshGridAfterSecurityReviewChanges function to handle missing endpoint data
window.refreshGridAfterSecurityReviewChanges = function(endpointId) {
  console.log(`Refreshing grid for endpoint ${endpointId}`);
  
  // Find the endpoint row in the grid
  const gridBody = document.getElementById("grid-body");
  if (!gridBody) {
    console.warn("Grid body not found, cannot update grid");
    return;
  }
  
  const rows = gridBody.querySelectorAll("tr");
  let rowToUpdate = null;
  
  // Find the row with the matching endpoint ID
  // The row might not have a data-endpoint-id attribute, so we need to check for buttons with the ID
  for (const row of rows) {
    // First try to get the endpoint ID from the data attribute
    let rowEndpointId = row.getAttribute("data-endpoint-id");
    
    // If not found, try to get it from the action buttons
    if (!rowEndpointId) {
      const actionBtn = row.querySelector(".add-review-btn, .view-history-btn");
      if (actionBtn) {
        rowEndpointId = actionBtn.getAttribute("data-endpoint-id");
      }
    }
    
    if (rowEndpointId === endpointId) {
      rowToUpdate = row;
      break;
    }
  }
  
  // Get the endpoint data
  const endpoint = typeof getEndpointById === 'function' ? getEndpointById(endpointId) : null;
  
  // If we can't find the row, try to refresh the entire grid if possible
  if (!rowToUpdate) {
    console.warn(`Could not find row for endpoint ${endpointId}, attempting to refresh entire grid`);
    if (typeof window.renderGrid === 'function') {
      window.renderGrid();
    } else if (typeof window.updateGrid === 'function') {
      window.updateGrid();
    }
    return;
  }
  
  // If we can't find the endpoint data, create a minimal endpoint object with the ID
  // This allows us to still update the grid row with the latest security review data
  const endpointToUse = endpoint || { 
    id: endpointId,
    // Try to extract other data from the row
    httpMethod: rowToUpdate.querySelector("td:nth-child(2)")?.textContent?.trim() || "Unknown",
    route: rowToUpdate.querySelector("td:nth-child(3)")?.textContent?.trim() || "Unknown Route"
  };
  
  // Get the latest security review for this endpoint
  const latestReview = typeof getLatestSecurityReview === 'function' ? 
    getLatestSecurityReview(endpointId) : null;
  
  // Update the security status cell (5th column, index 4)
  const securityStatusCell = rowToUpdate.querySelector("td:nth-child(5)");
  if (securityStatusCell) {
    if (typeof createSecurityStatusCell === 'function') {
      securityStatusCell.innerHTML = createSecurityStatusCell(endpointToUse);
    } else if (latestReview) {
      const statusClass = getSecurityStatusClass(latestReview.securityStatus);
      securityStatusCell.innerHTML = `<span class="security-status ${statusClass}">${latestReview.securityStatus}</span>`;
    } else {
      securityStatusCell.innerHTML = '<span class="security-status security-status-unknown">Not Reviewed</span>';
    }
  }
  
  // Update the review date cell (6th column, index 5)
  const reviewDateCell = rowToUpdate.querySelector("td:nth-child(6)");
  if (reviewDateCell) {
    if (typeof createReviewDateCell === 'function') {
      reviewDateCell.innerHTML = createReviewDateCell(endpointToUse);
    } else if (latestReview) {
      reviewDateCell.textContent = formatReviewDate(latestReview.reviewDateTime);
    } else {
      reviewDateCell.innerHTML = '<span class="text-muted">No reviews</span>';
    }
  }
  
  // Update the actions cell to ensure "View History" button is shown (7th column, index 6)
  const actionsCell = rowToUpdate.querySelector("td:nth-child(7)");
  if (actionsCell) {
    if (typeof createSecurityActionsCell === 'function') {
      actionsCell.innerHTML = createSecurityActionsCell(endpointToUse);
    } else {
      // Fallback: update the actions manually
      const hasReviews = getSecurityReviewsForEndpoint(endpointId).length > 0;
      const currentHTML = actionsCell.innerHTML;
      
      if (hasReviews && !currentHTML.includes("view-history-btn")) {
        // Add history button if it doesn't exist
        const addBtn = actionsCell.querySelector(".add-review-btn");
        if (addBtn) {
          const httpMethod = addBtn.getAttribute("data-http-method") || "Unknown";
          const route = addBtn.getAttribute("data-route") || "Unknown Route";
          
          const historyBtn = document.createElement("button");
          historyBtn.className = "security-action-btn view-history-btn";
          historyBtn.setAttribute("data-endpoint-id", endpointId);
          historyBtn.setAttribute("data-http-method", httpMethod);
          historyBtn.setAttribute("data-route", route);
          historyBtn.setAttribute("title", "View Review History");
          historyBtn.innerHTML = '<i class="bi bi-clock-history"></i> History';
          
          actionsCell.querySelector(".security-actions").appendChild(historyBtn);
        }
      }
    }
  }
  
  // Re-initialize action buttons to ensure event handlers are attached
  if (typeof initSecurityActionButtons === 'function') {
    initSecurityActionButtons();
  }
  
  // If the detail view is currently showing this endpoint, refresh it too
  const currentEndpoint = window.AppState ? window.AppState.getCurrentEndpoint() : window.currentEndpoint;
  let currentEndpointId = null;
  
  if (currentEndpoint) {
    // The current endpoint might be a wrapper object with diffType
    currentEndpointId = currentEndpoint.id || 
                       (currentEndpoint.newValue && currentEndpoint.newValue.id) ||
                       (currentEndpoint.oldValue && currentEndpoint.oldValue.id);
  }
  
  console.log(`Current endpoint in detail view: ${currentEndpointId || 'none'}`);
  console.log(`Endpoint we're updating: ${endpointId}`);
  
  if (currentEndpointId === endpointId) {
    console.log("Refreshing detail view with updated endpoint data");
    if (typeof window.refreshDetailView === 'function') {
      window.refreshDetailView();
    } else if (typeof window.DetailView === 'object' && typeof window.DetailView.refreshDetailView === 'function') {
      window.DetailView.refreshDetailView();
    } else {
      console.warn("refreshDetailView function not found, trying showDetailView");
      if (typeof window.showDetailView === 'function') {
        window.showDetailView(currentEndpoint);
      } else if (typeof window.DetailView === 'object' && typeof window.DetailView.showDetailView === 'function') {
        window.DetailView.showDetailView(currentEndpoint);
      }
    }
  } else {
    console.log("Detail view not showing this endpoint or no current endpoint");
  }
};

// Note: Column resizing functionality is handled by the main column-resizer.js module
// The initMainGridColumnResizing function has been removed to avoid conflicts

// Export functions for browser environment
if (typeof window !== "undefined") {
  // Main grid column resizing is handled by column-resizer.js
}
