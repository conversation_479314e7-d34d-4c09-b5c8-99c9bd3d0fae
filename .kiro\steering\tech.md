# Technology Stack

## Frontend Technologies

- **HTML5** - Semantic markup with <PERSON><PERSON><PERSON> integration
- **CSS3** - Custom styling with CSS variables and flexbox layout
- **Vanilla JavaScript** - No frameworks, pure DOM manipulation
- **Bootstrap 5.3** - UI components and responsive grid system

## Build System

- **Static Files** - No build process required
- **Direct Browser Loading** - Can be served from any web server or opened directly

## Libraries & Dependencies

- **Bootstrap CSS/JS** - Loaded via CDN (v5.3.0-alpha1)
- **No JavaScript frameworks** - Pure vanilla JS implementation

## File Structure

- Static web application with separate concerns
- CSS and JS files organized in dedicated folders
- Data files in JSON format for endpoint information

## Development Commands

Since this is a static web application, no build commands are required:

```bash
# Serve locally (any method)
python -m http.server 8000
# or
npx serve .
# or open index.html directly in browser

# Data processing (if needed)
node scripts/anon.js
```

## Browser Compatibility

- Modern browsers supporting ES6+ features
- CSS Grid and Flexbox support required
- File API support for JSON file loading
