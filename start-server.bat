@echo off
REM Start the API Endpoint Explorer application
REM This script starts a local HTTP server to serve the application

echo Starting API Endpoint Explorer...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is not installed or not in PATH.
    echo Please install Python 3 and try again.
    echo.
    echo Alternatively, you can:
    echo 1. Install Python from https://python.org
    echo 2. Or use any other HTTP server like:
    echo    - npx http-server (if you have Node.js)
    echo    - php -S localhost:8000 (if you have PHP)
    pause
    exit /b 1
)

echo Python found. Starting server...
echo.
echo The application will be available at: http://localhost:8000/index.html
echo Press Ctrl+C to stop the server
echo.

python server.py

pause
